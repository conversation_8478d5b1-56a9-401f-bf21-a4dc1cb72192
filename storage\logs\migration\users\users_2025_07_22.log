[2025-07-22 10:12:29] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:12:29] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:12:36] local.INFO: WordPress API connection validated  
[2025-07-22 10:12:36] local.INFO: Database connection validated  
[2025-07-22 10:12:36] local.INFO: Starting users migration  
[2025-07-22 10:12:36] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:12:41] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:12:41] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:48] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:12:48] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 5 errors  
[2025-07-22 10:12:48] local.INFO: Starting users migration for clinic 27  
[2025-07-22 10:12:48] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:53] local.INFO: Found 1 users for clinic 27  
[2025-07-22 10:12:53] local.INFO: Clinic 27 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:12:53] local.INFO: Starting users migration for clinic 26  
[2025-07-22 10:12:53] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:59] local.INFO: Found 1 users for clinic 26  
[2025-07-22 10:12:59] local.INFO: Clinic 26 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:12:59] local.INFO: Starting users migration for clinic 22  
[2025-07-22 10:12:59] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:13:04] local.INFO: Found 1 users for clinic 22  
[2025-07-22 10:13:04] local.INFO: Clinic 22 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:13:04] local.INFO: Starting users migration for clinic 16  
[2025-07-22 10:13:04] local.INFO: Making KiviCare API request: laravel_get_clinic_users  

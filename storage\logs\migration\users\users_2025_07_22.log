[2025-07-22 10:12:29] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:12:29] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:12:36] local.INFO: WordPress API connection validated  
[2025-07-22 10:12:36] local.INFO: Database connection validated  
[2025-07-22 10:12:36] local.INFO: Starting users migration  
[2025-07-22 10:12:36] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:12:41] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:12:41] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:48] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:12:48] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 5 errors  
[2025-07-22 10:12:48] local.INFO: Starting users migration for clinic 27  
[2025-07-22 10:12:48] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:53] local.INFO: Found 1 users for clinic 27  
[2025-07-22 10:12:53] local.INFO: Clinic 27 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:12:53] local.INFO: Starting users migration for clinic 26  
[2025-07-22 10:12:53] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:12:59] local.INFO: Found 1 users for clinic 26  
[2025-07-22 10:12:59] local.INFO: Clinic 26 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:12:59] local.INFO: Starting users migration for clinic 22  
[2025-07-22 10:12:59] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:13:04] local.INFO: Found 1 users for clinic 22  
[2025-07-22 10:13:04] local.INFO: Clinic 22 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 10:13:04] local.INFO: Starting users migration for clinic 16  
[2025-07-22 10:13:04] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:16:55] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:16:55] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:17:00] local.INFO: WordPress API connection validated  
[2025-07-22 10:17:00] local.INFO: Database connection validated  
[2025-07-22 10:17:00] local.INFO: Starting users migration  
[2025-07-22 10:17:00] local.INFO: Running in DRY RUN mode  
[2025-07-22 10:17:00] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:17:00] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:17:05] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:17:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:17:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:17:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:17:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:17:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:17:05] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 10:17:05] local.INFO: Migration completed - Processed: 5, Skipped: 0, Errors: 0  
[2025-07-22 10:17:05] local.INFO: Migration command completed successfully  
[2025-07-22 10:17:28] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:17:28] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:17:32] local.INFO: WordPress API connection validated  
[2025-07-22 10:17:32] local.INFO: Database connection validated  
[2025-07-22 10:17:32] local.INFO: Starting users migration  
[2025-07-22 10:17:32] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:17:32] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:17:37] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:17:37] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 5 errors  
[2025-07-22 10:17:37] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 5  
[2025-07-22 10:17:37] local.INFO: Migration command completed successfully  
[2025-07-22 10:18:31] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:18:31] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:18:36] local.INFO: WordPress API connection validated  
[2025-07-22 10:18:36] local.INFO: Database connection validated  
[2025-07-22 10:18:36] local.INFO: Starting users migration  
[2025-07-22 10:18:36] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:18:36] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:18:40] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:18:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 2, Role: provider  
[2025-07-22 10:18:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 5, Role: staff  
[2025-07-22 10:18:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 6, Role: clinic_admin  
[2025-07-22 10:18:42] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 2 errors  
[2025-07-22 10:18:42] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 2  
[2025-07-22 10:18:42] local.INFO: Migration command completed successfully  
[2025-07-22 10:19:48] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:19:48] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:19:54] local.INFO: WordPress API connection validated  
[2025-07-22 10:19:54] local.INFO: Database connection validated  
[2025-07-22 10:19:54] local.INFO: Starting users migration  
[2025-07-22 10:19:54] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:19:54] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:19:58] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:19:58] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:19:58] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:19:58] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:19:58] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:19:58] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:19:58] local.INFO: Clinic 40 completed: 0 processed, 5 skipped, 0 errors  
[2025-07-22 10:19:58] local.INFO: Migration completed - Processed: 0, Skipped: 5, Errors: 0  
[2025-07-22 10:19:58] local.INFO: Migration command completed successfully  
[2025-07-22 10:21:12] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:21:12] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:21:15] local.INFO: WordPress API connection validated  
[2025-07-22 10:21:15] local.INFO: Database connection validated  
[2025-07-22 10:21:15] local.INFO: Starting users migration  
[2025-07-22 10:21:15] local.INFO: Starting users migration for clinic 14  
[2025-07-22 10:21:15] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:21:19] local.INFO: Found 524 users for clinic 14  
[2025-07-22 10:21:19] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 7, Role: provider  
[2025-07-22 10:21:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 8, Role: provider  
[2025-07-22 10:21:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 9, Role: provider  
[2025-07-22 10:21:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 10, Role: provider  
[2025-07-22 10:21:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 11, Role: provider  
[2025-07-22 10:21:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 12, Role: provider  
[2025-07-22 10:21:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 13, Role: provider  
[2025-07-22 10:21:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 14, Role: provider  
[2025-07-22 10:21:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 15, Role: provider  
[2025-07-22 10:21:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 16, Role: provider  
[2025-07-22 10:21:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 17, Role: provider  
[2025-07-22 10:21:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 18, Role: provider  
[2025-07-22 10:21:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 19, Role: provider  
[2025-07-22 10:21:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 20, Role: provider  
[2025-07-22 10:21:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 21, Role: provider  
[2025-07-22 10:21:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 22, Role: provider  
[2025-07-22 10:21:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 23, Role: provider  
[2025-07-22 10:21:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 24, Role: provider  
[2025-07-22 10:21:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 25, Role: provider  
[2025-07-22 10:21:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 26, Role: provider  
[2025-07-22 10:21:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:21:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 27, Role: provider  
[2025-07-22 10:45:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 529, Role: staff  
[2025-07-22 10:45:00] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:45:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 530, Role: clinic_admin  
[2025-07-22 10:45:00] local.INFO: Clinic 14 completed: 23 processed, 0 skipped, 501 errors  
[2025-07-22 10:45:00] local.INFO: Migration completed - Processed: 23, Skipped: 0, Errors: 501  
[2025-07-22 10:45:00] local.INFO: Migration command completed successfully  
[2025-07-22 10:45:11] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:45:11] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:45:13] local.INFO: WordPress API connection validated  
[2025-07-22 10:45:13] local.INFO: Database connection validated  
[2025-07-22 10:45:13] local.INFO: Starting users migration  
[2025-07-22 10:45:13] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:45:13] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:45:15] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:45:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:45:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:45:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:45:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:45:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 10:45:15] local.INFO: Clinic 40 completed: 0 processed, 5 skipped, 0 errors  
[2025-07-22 10:45:15] local.INFO: Migration completed - Processed: 0, Skipped: 5, Errors: 0  
[2025-07-22 10:45:15] local.INFO: Migration command completed successfully  
[2025-07-22 10:45:59] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:45:59] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:46:03] local.INFO: WordPress API connection validated  
[2025-07-22 10:46:03] local.INFO: Database connection validated  
[2025-07-22 10:46:03] local.INFO: Starting users migration  
[2025-07-22 10:46:03] local.INFO: Running in DRY RUN mode  
[2025-07-22 10:46:03] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:46:03] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:46:05] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:46:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:46:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:46:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:46:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:46:05] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 10:46:05] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 10:46:05] local.INFO: Migration completed - Processed: 5, Skipped: 0, Errors: 0  
[2025-07-22 10:46:05] local.INFO: Migration command completed successfully  

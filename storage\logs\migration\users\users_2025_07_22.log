[2025-07-22 09:27:12] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 09:27:12] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 09:27:18] local.INFO: WordPress API connection validated  
[2025-07-22 09:27:19] local.INFO: Database connection validated  
[2025-07-22 09:27:19] local.INFO: Starting users migration  
[2025-07-22 09:27:19] local.INFO: Running in DRY RUN mode  
[2025-07-22 09:27:19] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 09:27:22] local.INFO: Starting users migration for clinic 40  
[2025-07-22 09:27:22] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:27] local.INFO: Found 5 users for clinic 40  
[2025-07-22 09:27:27] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:27] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:27] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:27] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:27] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:27] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:27] local.INFO: Starting users migration for clinic 27  
[2025-07-22 09:27:27] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:33] local.INFO: Found 1 users for clinic 27  
[2025-07-22 09:27:33] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:33] local.INFO: Clinic 27 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:33] local.INFO: Starting users migration for clinic 26  
[2025-07-22 09:27:33] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:38] local.INFO: Found 1 users for clinic 26  
[2025-07-22 09:27:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:38] local.INFO: Clinic 26 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:38] local.INFO: Starting users migration for clinic 22  
[2025-07-22 09:27:38] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:42] local.INFO: Found 1 users for clinic 22  
[2025-07-22 09:27:42] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:42] local.INFO: Clinic 22 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:42] local.INFO: Starting users migration for clinic 16  
[2025-07-22 09:27:42] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:48] local.INFO: Found 7 users for clinic 16  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:48] local.INFO: Clinic 16 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:48] local.INFO: Starting users migration for clinic 15  
[2025-07-22 09:27:48] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:53] local.INFO: Found 5 users for clinic 15  
[2025-07-22 09:27:53] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:53] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:53] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:53] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:53] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:53] local.INFO: Clinic 15 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:53] local.INFO: Starting users migration for clinic 14  
[2025-07-22 09:27:53] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:27:57] local.INFO: Found 524 users for clinic 14  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would migrate user majetyanupam+_878^<EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would migrate user peterclift123**@gmail.com with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:27:57] local.INFO: Clinic 14 completed: 524 processed, 0 skipped, 0 errors  
[2025-07-22 09:27:57] local.INFO: Starting users migration for clinic 13  
[2025-07-22 09:27:57] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:01] local.INFO: Found 2 users for clinic 13  
[2025-07-22 09:28:01] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:01] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:01] local.INFO: Clinic 13 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:01] local.INFO: Starting users migration for clinic 12  
[2025-07-22 09:28:01] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:06] local.INFO: Found 36 users for clinic 12  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:06] local.INFO: Clinic 12 completed: 36 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:06] local.INFO: Starting users migration for clinic 11  
[2025-07-22 09:28:06] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:10] local.INFO: Found 276 users for clinic 11  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:10] local.INFO: Clinic 11 completed: 276 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:10] local.INFO: Starting users migration for clinic 10  
[2025-07-22 09:28:10] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:14] local.INFO: Found 3 users for clinic 10  
[2025-07-22 09:28:14] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:14] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:14] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:14] local.INFO: Clinic 10 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:14] local.INFO: Starting users migration for clinic 9  
[2025-07-22 09:28:14] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:19] local.INFO: Found 6 users for clinic 9  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:19] local.INFO: Clinic 9 completed: 6 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:19] local.INFO: Starting users migration for clinic 8  
[2025-07-22 09:28:19] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:22] local.INFO: Found 11 users for clinic 8  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:22] local.INFO: Clinic 8 completed: 11 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:22] local.INFO: Starting users migration for clinic 6  
[2025-07-22 09:28:22] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:26] local.INFO: Found 3 users for clinic 6  
[2025-07-22 09:28:26] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:26] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:26] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:26] local.INFO: Clinic 6 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:26] local.INFO: Starting users migration for clinic 5  
[2025-07-22 09:28:26] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:30] local.INFO: Found 16 users for clinic 5  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:30] local.INFO: Clinic 5 completed: 16 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:30] local.INFO: Starting users migration for clinic 4  
[2025-07-22 09:28:30] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:34] local.INFO: No users found for clinic 4  
[2025-07-22 09:28:34] local.INFO: Starting users migration for clinic 2  
[2025-07-22 09:28:34] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:38] local.INFO: Found 7 users for clinic 2  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:38] local.INFO: Clinic 2 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:38] local.INFO: Starting users migration for clinic 1  
[2025-07-22 09:28:38] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:28:43] local.INFO: Found 54 users for clinic 1  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:28:43] local.INFO: Clinic 1 completed: 54 processed, 0 skipped, 0 errors  
[2025-07-22 09:28:43] local.INFO: Migration completed - Processed: 958, Skipped: 0, Errors: 0  
[2025-07-22 09:28:43] local.INFO: Migration command completed successfully  
[2025-07-22 09:57:51] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 09:57:51] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 09:57:57] local.INFO: WordPress API connection validated  
[2025-07-22 09:57:57] local.INFO: Database connection validated  
[2025-07-22 09:57:57] local.INFO: Starting users migration  
[2025-07-22 09:57:57] local.INFO: Running in DRY RUN mode  
[2025-07-22 09:57:57] local.INFO: Starting users migration for clinic 14  
[2025-07-22 09:57:57] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 09:58:02] local.INFO: Found 524 users for clinic 14  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would migrate user majetyanupam+_878^<EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would migrate user peterclift123**@gmail.com with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 09:58:02] local.INFO: Clinic 14 completed: 524 processed, 0 skipped, 0 errors  
[2025-07-22 09:58:02] local.INFO: Migration completed - Processed: 524, Skipped: 0, Errors: 0  
[2025-07-22 09:58:02] local.INFO: Migration command completed successfully  

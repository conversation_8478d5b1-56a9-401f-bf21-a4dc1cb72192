<?php

namespace App\Services\Migration;

use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DataTransformer
{
    /**
     * Transform WordPress clinic data to Laravel format
     */
    public function transformClinic($wpClinic)
    {
        return [
            'name' => $this->sanitizeString($wpClinic['name'] ?? ''),
            'email' => $this->sanitizeEmail($wpClinic['email'] ?? ''),
            'phone' => $this->sanitizePhone($wpClinic['telephone_no'] ?? ''),
            'website' => $this->sanitizeString($wpClinic['website'] ?? ''),
            'address' => $this->sanitizeString($wpClinic['address'] ?? ''),
            'city' => $this->sanitizeString($wpClinic['city'] ?? ''),
            'state' => $this->sanitizeString($wpClinic['state'] ?? ''),
            'country' => $this->sanitizeString($wpClinic['country'] ?? ''),
            'country_code' => $this->sanitizeString($wpClinic['country_code'] ?? ''),
            'country_calling_code' => $this->sanitizeString($wpClinic['country_calling_code'] ?? ''),
            'postal_code' => $this->sanitizeString($wpClinic['postal_code'] ?? ''),
            'specialties' => $this->transformSpecialties($wpClinic['specialties'] ?? []),
            'is_active' => $this->convertBoolean($wpClinic['status'] ?? 0),
            'logo' => $this->sanitizeString($wpClinic['clinic_logo'] ?? ''),
            'profile_image' => $this->sanitizeString($wpClinic['profile_image'] ?? ''),
            'clinic_admin_id' => $this->sanitizeInteger($wpClinic['clinic_admin_id'] ?? null),
            'clinic_admin_email' => $this->sanitizeEmail($wpClinic['clinic_admin_email'] ?? ''),
            'allow_no_of_doc' => $this->sanitizeString($wpClinic['allow_no_of_doc'] ?? ''),
            'settings' => $this->transformSettings($wpClinic['extra'] ?? []),
            'wp_clinic_id' => (int) $wpClinic['id'],
            'created_at' => $this->convertDateTime($wpClinic['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress user data to Laravel format
     */
    public function transformUser($wpUser, $clinicId = null)
    {
        // Extract role from WordPress user data
        $wpRole = $this->extractUserRole($wpUser);
        $role = $this->convertRole($wpRole);

        // Parse basic_data JSON field
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');

        // Extract first_name and last_name from WordPress fields or basic_data
        $firstName = $this->sanitizeString($wpUser['first_name'] ?? $basicData['first_name'] ?? '');
        $lastName = $this->sanitizeString($wpUser['last_name'] ?? $basicData['last_name'] ?? '');

        return [
            'name' => $this->sanitizeString($wpUser['display_name'] ?? ''),
            'email' => $this->sanitizeEmail($wpUser['user_email'] ?? ''),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone_number' => $this->sanitizePhone($basicData['mobile_number'] ?? ''),
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'date_of_birth' => $this->convertDate($basicData['dob'] ?? null),
            'address' => $this->sanitizeString($basicData['address'] ?? ''),
            'role' => $role,
            'clinic_id' => $clinicId,
            'is_active' => true,
            'wp_user_id' => (int) $wpUser['ID'],
            'wp_basic_data' => $basicData, // Store original data for reference
            'email_verified_at' => null,
            'password' => $this->generateTemporaryPassword($wpUser['ID']),
            'password_change_required' => true,
            'signup_source' => 'system_created',
            'created_at' => $this->convertDateTime($wpUser['user_registered'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress user data for Patient record
     */
    public function transformPatientData($wpUser, $userId, $clinicId)
    {
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');

        return [
            'user_id' => $userId,
            'clinic_id' => $clinicId,
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'date_of_birth' => $this->convertDate($basicData['dob'] ?? null),

            // Insurance information
            'insurance_provider' => $this->sanitizeString($basicData['insurance_provider'] ?? ''),
            'insurance_policy_number' => $this->sanitizeString($basicData['insurance_no'] ?? ''),

            // Emergency contact (using GP details as emergency contact)
            'emergency_contact_name' => $this->sanitizeString($basicData['registered_gp_name'] ?? ''),
            'emergency_contact_phone' => $this->sanitizeString($basicData['mobile_number'] ?? ''),
            'emergency_contact_relationship' => 'GP',

            // Additional patient-specific data from WordPress
            'emergency_contact' => $this->buildEmergencyContactData($basicData),
            'medical_history' => $this->buildMedicalHistory($basicData),
            'current_medications' => [],

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Build emergency contact data from WordPress basic_data
     */
    protected function buildEmergencyContactData($basicData)
    {
        $emergencyContact = [];

        if (!empty($basicData['registered_gp_name'])) {
            $emergencyContact['gp_name'] = $basicData['registered_gp_name'];
        }

        if (!empty($basicData['registered_gp_address'])) {
            $emergencyContact['gp_address'] = $basicData['registered_gp_address'];
        }

        return $emergencyContact;
    }

    /**
     * Build medical history from WordPress basic_data
     */
    protected function buildMedicalHistory($basicData)
    {
        $medicalHistory = [];

        if (!empty($basicData['nhs'])) {
            $medicalHistory['nhs_number'] = $basicData['nhs'];
        }

        if (!empty($basicData['blood_group'])) {
            $medicalHistory['blood_group'] = $basicData['blood_group'];
        }

        return $medicalHistory;
    }

    /**
     * Transform WordPress user data for Provider record
     */
    public function transformProviderData($wpUser, $userId)
    {
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');
        $specialties = $this->transformSpecialties($basicData['specialties'] ?? []);

        return [
            'user_id' => $userId,
            'specialization' => !empty($specialties) ? $specialties[0]['label'] ?? 'General Practice' : 'General Practice',
            'license_number' => $this->buildLicenseNumber($basicData),
            'verification_status' => 'pending',
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'pricing' => $this->transformPricing($basicData),

            // Additional provider data from WordPress
            'bio' => $this->buildProviderBio($basicData),
            'education' => $this->buildEducationString($basicData['qualifications'] ?? []),
            'languages' => ['English'], // Default, can be enhanced later
            'accepts_insurance' => !empty($basicData['insurance_provider']),
            'insurance_providers' => !empty($basicData['insurance_provider']) ? [$basicData['insurance_provider']] : [],

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Build license number with prefix
     */
    protected function buildLicenseNumber($basicData)
    {
        $gmcNo = $this->sanitizeString($basicData['gmc_no'] ?? '');
        $prefix = $this->sanitizeString($basicData['registration_prefix'] ?? '');

        if (!empty($prefix) && !empty($gmcNo)) {
            return strtoupper($prefix) . ': ' . $gmcNo;
        }

        return $gmcNo;
    }

    /**
     * Build provider bio from WordPress data
     */
    protected function buildProviderBio($basicData)
    {
        $bio = [];

        if (!empty($basicData['no_of_experience'])) {
            $bio[] = "Experience: {$basicData['no_of_experience']} years";
        }

        if (!empty($basicData['specialties'])) {
            $specialties = $this->transformSpecialties($basicData['specialties']);
            if (!empty($specialties)) {
                $specialtyNames = array_column($specialties, 'label');
                $bio[] = "Specialties: " . implode(', ', $specialtyNames);
            }
        }

        return implode('. ', $bio);
    }

    /**
     * Build education string from qualifications array
     */
    protected function buildEducationString($qualifications)
    {
        if (empty($qualifications) || !is_array($qualifications)) {
            return '';
        }

        $educationParts = [];
        foreach ($qualifications as $qualification) {
            if (is_array($qualification)) {
                $parts = [];
                if (!empty($qualification['degree'])) {
                    $parts[] = $qualification['degree'];
                }
                if (!empty($qualification['university'])) {
                    $parts[] = $qualification['university'];
                }
                if (!empty($qualification['year'])) {
                    $parts[] = $qualification['year'];
                }
                if (!empty($parts)) {
                    $educationParts[] = implode(' - ', $parts);
                }
            }
        }

        return implode('; ', $educationParts);
    }

    /**
     * Transform pricing data for providers
     */
    protected function transformPricing($basicData)
    {
        $pricing = [];

        if (!empty($basicData['price_type']) && !empty($basicData['price'])) {
            if ($basicData['price_type'] === 'range') {
                $priceRange = explode('-', $basicData['price']);
                $pricing['consultation_fee'] = [
                    'min' => (float) ($priceRange[0] ?? 0),
                    'max' => (float) ($priceRange[1] ?? 0),
                ];
            } else {
                $pricing['consultation_fee'] = (float) $basicData['price'];
            }
        }

        if (!empty($basicData['video_price'])) {
            $pricing['video_consultation_fee'] = (float) $basicData['video_price'];
        }

        return $pricing;
    }

    /**
     * Extract user role from WordPress user data (handles different formats)
     */
    protected function extractUserRole($wpUser)
    {
        // First check for user_type field from the API response
        if (isset($wpUser['user_type']) && !empty($wpUser['user_type'])) {
            return $wpUser['user_type'];
        }

        // Try different possible role formats from WordPress
        if (isset($wpUser['roles']) && is_array($wpUser['roles']) && !empty($wpUser['roles'])) {
            return $wpUser['roles'][0];
        }

        if (isset($wpUser['role']) && !empty($wpUser['role'])) {
            return $wpUser['role'];
        }

        if (isset($wpUser['user_role']) && !empty($wpUser['user_role'])) {
            return $wpUser['user_role'];
        }

        if (isset($wpUser['meta']['wp_capabilities']) && is_array($wpUser['meta']['wp_capabilities'])) {
            $capabilities = array_keys($wpUser['meta']['wp_capabilities']);
            return $capabilities[0] ?? 'patient';
        }

        // Default fallback
        return 'patient';
    }

    /**
     * Transform WordPress service data to Laravel format
     */
    public function transformService($wpService)
    {
        return [
            'name' => $this->sanitizeString($wpService['name'] ?? ''),
            'description' => $this->sanitizeString($wpService['description'] ?? ''),
            'type' => $this->sanitizeString($wpService['type'] ?? 'consultation'),
            'price' => $this->convertDecimal($wpService['price'] ?? 0),
            'duration_minutes' => (int) ($wpService['duration'] ?? 30),
            'is_active' => $this->convertBoolean($wpService['status'] ?? 0),
            'clinic_id' => (int) $wpService['clinic_id'],
            'wp_service_id' => (int) $wpService['id'],
            'created_at' => $this->convertDateTime($wpService['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress appointment data to Laravel format
     */
    public function transformAppointment($wpAppointment, $patientId, $providerId)
    {
        $scheduledAt = $this->combineDateTime(
            $wpAppointment['appointment_start_date'] ?? null,
            $wpAppointment['appointment_start_time'] ?? null
        );

        $endedAt = $this->combineDateTime(
            $wpAppointment['appointment_end_date'] ?? null,
            $wpAppointment['appointment_end_time'] ?? null
        );

        return [
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'date' => $this->convertDate($wpAppointment['appointment_start_date'] ?? null),
            'time_slot' => $wpAppointment['appointment_start_time'] ?? null,
            'scheduled_at' => $scheduledAt,
            'ended_at' => $endedAt,
            'duration_minutes' => $this->calculateDuration($scheduledAt, $endedAt),
            'reason' => $this->sanitizeString($wpAppointment['description'] ?? ''),
            'status' => $this->convertAppointmentStatus($wpAppointment['status'] ?? 0),
            'consultation_type' => $this->sanitizeString($wpAppointment['visit_type'] ?? 'in_person'),
            'is_telemedicine' => $this->isTelemedicine($wpAppointment['visit_type'] ?? ''),
            'payment_status' => 'pending',
            'wp_appointment_id' => (int) $wpAppointment['id'],
            'created_at' => $this->convertDateTime($wpAppointment['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress encounter data to Laravel consultation format
     */
    public function transformConsultation($wpEncounter, $appointmentId, $patientId, $providerId)
    {
        return [
            'appointment_id' => $appointmentId,
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'clinic_id' => (int) $wpEncounter['clinic_id'],
            'consultation_type' => 'in_person',
            'status' => $this->convertConsultationStatus($wpEncounter['status'] ?? 0),
            'consultation_date' => $this->convertDateTime($wpEncounter['encounter_date'] ?? null),
            'consultation_mode' => 'in_person',
            'is_telemedicine' => false,
            'vital_signs' => $this->transformVitalSigns($wpEncounter['vitals'] ?? []),
            'main_tabs' => $this->transformConsultationTabs($wpEncounter['tabs'] ?? []),
            'additional_tabs' => [],
            'wp_encounter_id' => (int) $wpEncounter['id'],
            'created_at' => $this->convertDateTime($wpEncounter['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress prescription data to Laravel format
     */
    public function transformPrescription($wpPrescription, $consultationId, $patientId, $providerId)
    {
        return [
            'consultation_id' => $consultationId,
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'medication_name' => $this->sanitizeString($wpPrescription['name'] ?? ''),
            'dosage' => $this->extractDosage($wpPrescription['name'] ?? ''),
            'frequency' => $this->sanitizeString($wpPrescription['frequency'] ?? ''),
            'duration' => $this->sanitizeString($wpPrescription['duration'] ?? ''),
            'instructions' => $this->sanitizeString($wpPrescription['instruction'] ?? ''),
            'status' => 'active',
            'wp_prescription_id' => (int) $wpPrescription['id'],
            'created_at' => $this->convertDateTime($wpPrescription['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    // Helper methods for data conversion

    /**
     * Convert WordPress role to Laravel role
     */
    public function convertRole($wpRole)
    {
        $mapping = config('migration.role_mapping', []);
        return $mapping[$wpRole] ?? 'patient';
    }

    /**
     * Convert appointment status
     */
    public function convertAppointmentStatus($status)
    {
        $mapping = config('migration.appointment_status_mapping', []);
        return $mapping[$status] ?? 'scheduled';
    }

    /**
     * Convert consultation status
     */
    public function convertConsultationStatus($status)
    {
        $mapping = config('migration.consultation_status_mapping', []);
        return $mapping[$status] ?? 'draft';
    }

    /**
     * Convert boolean value
     */
    public function convertBoolean($value)
    {
        return $value == 1 || $value === true || $value === 'true';
    }

    /**
     * Convert date string to Carbon instance
     */
    public function convertDate($date)
    {
        if (!$date || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return null;
        }

        try {
            return Carbon::parse($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Convert datetime string to Carbon instance
     */
    public function convertDateTime($datetime)
    {
        if (!$datetime || $datetime === '0000-00-00 00:00:00') {
            return null;
        }

        try {
            return Carbon::parse($datetime);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Combine date and time into datetime
     */
    public function combineDateTime($date, $time)
    {
        if (!$date || !$time) {
            return null;
        }

        try {
            return Carbon::parse($date . ' ' . $time);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Calculate duration between two datetime objects
     */
    public function calculateDuration($start, $end)
    {
        if (!$start || !$end) {
            return 30; // Default 30 minutes
        }

        try {
            return $start->diffInMinutes($end);
        } catch (\Exception $e) {
            return 30;
        }
    }

    /**
     * Check if appointment is telemedicine
     */
    public function isTelemedicine($visitType)
    {
        $telemedicineTypes = ['video', 'phone', 'online', 'virtual', 'telemedicine'];
        return in_array(strtolower($visitType), $telemedicineTypes);
    }

    /**
     * Sanitize string input
     */
    public function sanitizeString($value)
    {
        return trim(strip_tags($value ?? ''));
    }

    /**
     * Sanitize email input
     */
    public function sanitizeEmail($email)
    {
        $email = trim(strtolower($email ?? ''));
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : null;
    }

    /**
     * Sanitize phone number
     */
    public function sanitizePhone($phone)
    {
        return preg_replace('/[^0-9+\-\s\(\)]/', '', $phone ?? '');
    }

    /**
     * Sanitize integer input
     */
    public function sanitizeInteger($value)
    {
        if ($value === null || $value === '' || $value === '0') {
            return null;
        }
        return (int) $value;
    }

    /**
     * Convert decimal value
     */
    public function convertDecimal($value)
    {
        return round(floatval($value ?? 0), 2);
    }

    /**
     * Convert gender value
     */
    public function convertGender($gender)
    {
        $gender = strtolower(trim($gender ?? ''));
        $validGenders = ['male', 'female', 'other'];
        return in_array($gender, $validGenders) ? $gender : null;
    }

    /**
     * Generate temporary password
     */
    public function generateTemporaryPassword($userId)
    {
        return Hash::make('temporary_password_' . $userId);
    }

    /**
     * Transform specialties array
     */
    public function transformSpecialties($specialties)
    {
        if (is_string($specialties)) {
            return json_decode($specialties, true) ?? [];
        }
        return is_array($specialties) ? $specialties : [];
    }

    /**
     * Transform qualifications array
     */
    public function transformQualifications($qualifications)
    {
        if (is_string($qualifications)) {
            return json_decode($qualifications, true) ?? [];
        }
        return is_array($qualifications) ? $qualifications : [];
    }

    /**
     * Parse basic_data JSON field from WordPress
     */
    public function parseBasicData($basicDataJson)
    {
        if (empty($basicDataJson)) {
            return [];
        }

        if (is_string($basicDataJson)) {
            $decoded = json_decode($basicDataJson, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($basicDataJson) ? $basicDataJson : [];
    }

    /**
     * Transform settings/extra data
     */
    public function transformSettings($extra)
    {
        if (is_string($extra)) {
            return json_decode($extra, true) ?? [];
        }
        return is_array($extra) ? $extra : [];
    }

    /**
     * Transform vital signs
     */
    public function transformVitalSigns($vitals)
    {
        return is_array($vitals) ? $vitals : [];
    }

    /**
     * Transform consultation tabs
     */
    public function transformConsultationTabs($tabs)
    {
        $transformed = [];
        
        if (is_array($tabs)) {
            foreach ($tabs as $tab) {
                $tabName = Str::slug($tab['tab_name'] ?? 'general');
                $transformed[$tabName] = [
                    [
                        'id' => 1,
                        'content' => $tab['content'] ?? '',
                        'created_at' => now()->toISOString(),
                    ]
                ];
            }
        }
        
        return $transformed;
    }

    /**
     * Transform WordPress bill data to Laravel format
     */
    public function transformBill($wpBill, $consultation, $appointment, $patientId, $providerId)
    {
        return [
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'clinic_id' => $consultation ? $consultation->clinic_id : ($appointment ? $appointment->clinic_id : null),
            'consultation_id' => $consultation ? $consultation->id : null,
            'appointment_id' => $appointment ? $appointment->id : null,
            'bill_number' => $this->generateBillNumber($wpBill['id']),
            'description' => $this->sanitizeString($wpBill['title'] ?? ''),
            'subtotal_amount' => $this->convertDecimal($wpBill['total_amount'] ?? 0),
            'discount_amount' => $this->convertDecimal($wpBill['discount'] ?? 0),
            'tax_amount' => 0, // WordPress doesn't seem to have separate tax
            'total_amount' => $this->convertDecimal($wpBill['total_amount'] ?? 0),
            'final_amount' => $this->convertDecimal($wpBill['actual_amount'] ?? 0),
            'status' => $this->convertBillStatus($wpBill['status'] ?? 0),
            'payment_status' => $this->sanitizeString($wpBill['payment_status'] ?? 'pending'),
            'due_date' => now()->addDays(30), // Default 30 days
            'notes' => '',
            'wp_bill_id' => (int) $wpBill['id'],
            'created_at' => $this->convertDateTime($wpBill['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Convert bill status
     */
    public function convertBillStatus($status)
    {
        $mapping = config('migration.bill_status_mapping', [
            0 => 'draft',
            1 => 'sent',
            2 => 'paid',
            3 => 'overdue',
        ]);
        return $mapping[$status] ?? 'draft';
    }

    /**
     * Generate bill number from WordPress ID
     */
    public function generateBillNumber($wpBillId)
    {
        return 'WP-' . str_pad($wpBillId, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Extract dosage from medication name
     */
    public function extractDosage($medicationName)
    {
        // Simple regex to extract dosage like "Aspirin 81mg" -> "81mg"
        if (preg_match('/(\d+(?:\.\d+)?\s*(?:mg|g|ml|mcg|units?))/i', $medicationName, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Simple transform methods for clinical migration (without pre-resolved IDs)
     */

    /**
     * Transform WordPress appointment data (simple version)
     */
    public function transformAppointmentSimple($wpAppointment)
    {
        $scheduledAt = $this->combineDateTime(
            $wpAppointment['appointment_start_date'] ?? null,
            $wpAppointment['appointment_start_time'] ?? null
        );

        $endedAt = $this->combineDateTime(
            $wpAppointment['appointment_end_date'] ?? null,
            $wpAppointment['appointment_end_time'] ?? null
        );

        return [
            'date' => $this->convertDate($wpAppointment['appointment_start_date'] ?? null),
            'time_slot' => $wpAppointment['appointment_start_time'] ?? null,
            'scheduled_at' => $scheduledAt,
            'ended_at' => $endedAt,
            'duration_minutes' => $this->calculateDuration($scheduledAt, $endedAt),
            'reason' => $this->sanitizeString($wpAppointment['description'] ?? ''),
            'status' => $this->convertAppointmentStatus($wpAppointment['status'] ?? 0),
            'consultation_type' => $this->sanitizeString($wpAppointment['visit_type'] ?? 'in_person'),
            'is_telemedicine' => $this->isTelemedicine($wpAppointment['visit_type'] ?? ''),
            'payment_status' => 'pending',
            'created_at' => $this->convertDateTime($wpAppointment['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress consultation data (simple version)
     */
    public function transformConsultationSimple($wpEncounter)
    {
        return [
            'consultation_type' => 'general',
            'status' => $this->convertConsultationStatus($wpEncounter['status'] ?? 0),
            'consultation_date' => $this->convertDateTime($wpEncounter['encounter_date'] ?? null),
            'consultation_mode' => 'in_person',
            'is_telemedicine' => false,
            'vital_signs' => $this->transformVitalSigns($wpEncounter['vitals'] ?? []),
            'main_tabs' => $this->transformConsultationTabs($wpEncounter['tabs'] ?? []),
            'additional_tabs' => [],
            'created_at' => $this->convertDateTime($wpEncounter['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress prescription data (simple version)
     */
    public function transformPrescriptionSimple($wpPrescription)
    {
        return [
            'prescription_number' => $wpPrescription['prescription_number'] ?? 'RX-' . uniqid(),
            'status' => $this->convertPrescriptionStatus($wpPrescription['status'] ?? 0),
            'type' => 'new',
            'prescribed_date' => $this->convertDate($wpPrescription['created_date'] ?? null),
            'clinical_indication' => $this->sanitizeString($wpPrescription['indication'] ?? ''),
            'additional_instructions' => $this->sanitizeString($wpPrescription['instructions'] ?? ''),
            'is_private' => false,
            'is_electronic' => true,
            'total_items' => 1,
            'created_at' => $this->convertDateTime($wpPrescription['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress medical history data (simple version)
     */
    public function transformMedicalHistory($wpHistory)
    {
        return [
            'type' => $this->sanitizeString($wpHistory['type'] ?? 'general'),
            'condition' => $this->sanitizeString($wpHistory['condition'] ?? ''),
            'description' => $this->sanitizeString($wpHistory['description'] ?? ''),
            'diagnosis_date' => $this->convertDate($wpHistory['diagnosis_date'] ?? null),
            'status' => $this->sanitizeString($wpHistory['status'] ?? 'active'),
            'severity' => $this->sanitizeString($wpHistory['severity'] ?? 'mild'),
            'notes' => $this->sanitizeString($wpHistory['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpHistory['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress bill data (simple version)
     */
    public function transformBillSimple($wpBill)
    {
        return [
            'bill_number' => $wpBill['bill_number'] ?? 'BILL-' . uniqid(),
            'bill_date' => $this->convertDate($wpBill['bill_date'] ?? null),
            'due_date' => $this->convertDate($wpBill['due_date'] ?? null),
            'subtotal' => (float) ($wpBill['subtotal'] ?? 0),
            'tax_amount' => (float) ($wpBill['tax_amount'] ?? 0),
            'discount_amount' => (float) ($wpBill['discount_amount'] ?? 0),
            'total_amount' => (float) ($wpBill['total_amount'] ?? 0),
            'paid_amount' => (float) ($wpBill['paid_amount'] ?? 0),
            'status' => $this->convertBillStatus($wpBill['status'] ?? 0), // Use existing method
            'payment_status' => $this->sanitizeString($wpBill['payment_status'] ?? 'pending'),
            'payment_method' => $this->sanitizeString($wpBill['payment_method'] ?? 'cash'),
            'notes' => $this->sanitizeString($wpBill['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpBill['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress medical problem data (simple version)
     */
    public function transformMedicalProblem($wpProblem)
    {
        return [
            'problem_type' => $this->sanitizeString($wpProblem['problem_type'] ?? ''),
            'description' => $this->sanitizeString($wpProblem['description'] ?? ''),
            'start_date' => $this->convertDate($wpProblem['start_date'] ?? null),
            'end_date' => $this->convertDate($wpProblem['end_date'] ?? null),
            'status' => $this->sanitizeString($wpProblem['status'] ?? 'active'),
            'severity' => $this->sanitizeString($wpProblem['severity'] ?? 'mild'),
            'outcome' => $this->sanitizeString($wpProblem['outcome'] ?? ''),
            'notes' => $this->sanitizeString($wpProblem['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpProblem['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress patient document data (simple version)
     */
    public function transformPatientDocument($wpDocument)
    {
        return [
            'document_name' => $this->sanitizeString($wpDocument['document_name'] ?? ''),
            'document_type' => $this->sanitizeString($wpDocument['document_type'] ?? 'general'),
            'file_path' => $this->sanitizeString($wpDocument['file_path'] ?? ''),
            'file_url' => $this->sanitizeString($wpDocument['file_url'] ?? ''),
            'file_size' => (int) ($wpDocument['file_size'] ?? 0),
            'mime_type' => $this->sanitizeString($wpDocument['mime_type'] ?? ''),
            'description' => $this->sanitizeString($wpDocument['description'] ?? ''),
            'is_private' => (bool) ($wpDocument['is_private'] ?? false),
            'uploaded_by' => $this->sanitizeString($wpDocument['uploaded_by'] ?? ''),
            'created_at' => $this->convertDateTime($wpDocument['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }
}

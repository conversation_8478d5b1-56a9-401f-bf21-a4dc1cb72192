<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('wp_user_id')->nullable()->unique()->after('id');

            // Additional fields for WordPress migration data
            $table->string('city')->nullable()->after('address');
            $table->string('state')->nullable()->after('city');
            $table->string('country')->nullable()->after('state');
            $table->string('postal_code')->nullable()->after('country');

            // Professional details (for doctors)
            $table->string('gmc_no')->nullable()->after('postal_code');
            $table->string('registration_prefix')->nullable()->after('gmc_no');
            $table->integer('no_of_experience')->nullable()->after('registration_prefix');
            $table->string('price_type')->nullable()->after('no_of_experience');
            $table->string('price')->nullable()->after('price_type');
            $table->decimal('video_price', 8, 2)->nullable()->after('price');
            $table->integer('time_slot')->nullable()->after('video_price');

            // Patient-specific fields
            $table->string('nhs_number')->nullable()->after('time_slot');
            $table->string('blood_group')->nullable()->after('nhs_number');
            $table->string('registered_gp_name')->nullable()->after('blood_group');
            $table->text('registered_gp_address')->nullable()->after('registered_gp_name');
            $table->string('insurance_provider')->nullable()->after('registered_gp_address');
            $table->string('insurance_no')->nullable()->after('insurance_provider');

            // JSON fields for complex data
            $table->json('specialties')->nullable()->after('insurance_no');
            $table->json('qualifications')->nullable()->after('specialties');
            $table->json('wp_basic_data')->nullable()->after('qualifications'); // Store original basic_data for reference
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'wp_user_id',
                'city',
                'state',
                'country',
                'postal_code',
                'gmc_no',
                'registration_prefix',
                'no_of_experience',
                'price_type',
                'price',
                'video_price',
                'time_slot',
                'nhs_number',
                'blood_group',
                'registered_gp_name',
                'registered_gp_address',
                'insurance_provider',
                'insurance_no',
                'specialties',
                'qualifications',
                'wp_basic_data'
            ]);
        });
    }
};

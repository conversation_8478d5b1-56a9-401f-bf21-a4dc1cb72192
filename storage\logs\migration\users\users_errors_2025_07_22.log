[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 702: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (702, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>@doctors.net.uk, Cuong, Nguyen, 07956527607, male, ?, 5 Harmont House, 20 Harley Street, provider, 2, 1, {"mobile_number":"07956527607","gender":"male","dob":null,"address":"5 Harmont House, 20 Harley Street","city":"London","state":"","country":null,"postal_code":"W1G 9PH","npi_no":"","qualifications":[{"degree":"MBBS","university":"London","year":"1995","file":null},{"degree":"BMAcS","university":"BMAcS","year":"1997","file":null},{"degree":"FRCS","university":"RCS","year":"1998","file":null}],"price_type":null,"price":null,"no_of_experience":"0","video_price":"0","specialties":[{"id":"18","label":"General Practice"}],"time_slot":null,"gmc_no":"4259451","registration_prefix":"GMC"}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"702","user_login":"Cuong_lDWf08","user_email":"<EMAIL>","user_nicename":"cuong_ldwf08","display_name":"Cuong Nguyen","user_registered":"2025-04-25 07:36:21","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07956527607\",\"gender\":\"male\",\"dob\":null,\"address\":\"5 Harmont House, 20 Harley Street\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"npi_no\":\"\",\"qualifications\":[{\"degree\":\"MBBS\",\"university\":\"London\",\"year\":\"1995\",\"file\":null},{\"degree\":\"BMAcS\",\"university\":\"BMAcS\",\"year\":\"1997\",\"file\":null},{\"degree\":\"FRCS\",\"university\":\"RCS\",\"year\":\"1998\",\"file\":null}],\"price_type\":null,\"price\":null,\"no_of_experience\":\"0\",\"video_price\":\"0\",\"specialties\":[{\"id\":\"18\",\"label\":\"General Practice\"}],\"time_slot\":null,\"gmc_no\":\"4259451\",\"registration_prefix\":\"GMC\"}","user_type":"kiviCare_doctor","clinic_id":"40","mapping_created_at":"2025-05-07 18:44:20"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 703: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (703, Test Patient, <EMAIL>, Test, Patient, 01234567889, male, 2025-04-01 00:00:00, 5 Harmont House , 20 Harley Street, patient, 2, 1, {"mobile_number":"01234567889","gender":"male","dob":"2025-04-01","nhs":null,"address":"5 Harmont House , 20 Harley Street","city":null,"state":"","country":null,"postal_code":"W1G 9PH","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"703","user_login":"Test_Wtyku2","user_email":"<EMAIL>","user_nicename":"test_wtyku2","display_name":"Test Patient","user_registered":"2025-04-25 07:39:28","first_name":"Test","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567889\",\"gender\":\"male\",\"dob\":\"2025-04-01\",\"nhs\":null,\"address\":\"5 Harmont House , 20 Harley Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-25 08:39:28"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 727: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (727, tester Patient, <EMAIL>, tester, Patient, 01234567880, male, 1999-01-01 00:00:00, 1 road, patient, 2, 1, {"mobile_number":"01234567880","gender":"male","dob":"1999-01-01","nhs":null,"address":"1 road","city":null,"state":"","country":null,"postal_code":"q13mm","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"727","user_login":"tester_d7p2eU","user_email":"<EMAIL>","user_nicename":"tester_d7p2eu","display_name":"tester Patient","user_registered":"2025-04-30 09:50:11","first_name":"tester","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567880\",\"gender\":\"male\",\"dob\":\"1999-01-01\",\"nhs\":null,\"address\":\"1 road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"q13mm\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-30 10:50:11"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 697: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (697, Anh Nga Nguyen, <EMAIL>, Anh Nga, Nguyen, 07939155900, female, ?, , staff, 2, 1, {"mobile_number":"07939155900","gender":"female","dob":"","address":null,"city":null,"state":"","country":null,"postal_code":null}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"697","user_login":"Anh_Nga_eKwkUd","user_email":"<EMAIL>","user_nicename":"anh_nga_ekwkud","display_name":"Anh Nga Nguyen","user_registered":"2025-04-24 12:35:12","first_name":"Anh Nga","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07939155900\",\"gender\":\"female\",\"dob\":\"\",\"address\":null,\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null}","user_type":"kiviCare_receptionist","clinic_id":"40","mapping_created_at":"0000-00-00 00:00:00"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 696: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (696, Cuong Nguyen, <EMAIL>, Cuong, Nguyen, 02076365150, ?, ?, , clinic_admin, 2, 1, {"first_name":"Cuong","last_name":"Nguyen","user_email":"<EMAIL>","mobile_number":"02076365150","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Cuong_XKlebw","user_pass":"Penthouse8!"}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"696","user_login":"Cuong_XKlebw","user_email":"<EMAIL>","user_nicename":"cuong_xklebw","display_name":"Cuong Nguyen","user_registered":"2025-04-24 10:31:55","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"first_name\":\"Cuong\",\"last_name\":\"Nguyen\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"02076365150\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Cuong_XKlebw\",\"user_pass\":\"Penthouse8!\"}","user_type":"kiviCare_clinic_admin","clinic_id":"40","mapping_created_at":"2025-04-24 11:31:55"} 
[2025-07-22 10:12:53] local.ERROR: Failed to migrate user ID 678: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (678, Mujtiba Mirza, <EMAIL>, Mujtiba, Mirza, 07557642461, ?, ?, , clinic_admin, 3, 1, {"first_name":"Mujtiba","last_name":"Mirza","user_email":"<EMAIL>","mobile_number":"07557642461","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Mujtiba_5Eiv4p","user_pass":"h0ney#Mujt4b4"}, ?, ?, 1, system_created, 2025-07-22 10:12:53, 2025-07-22 10:12:53)) {"ID":"678","user_login":"Mujtiba_5Eiv4p","user_email":"<EMAIL>","user_nicename":"mujtiba_5eiv4p","display_name":"Mujtiba Mirza","user_registered":"2025-04-23 07:12:07","first_name":"Mujtiba","last_name":"Mirza","phone":null,"basic_data":"{\"first_name\":\"Mujtiba\",\"last_name\":\"Mirza\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"07557642461\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Mujtiba_5Eiv4p\",\"user_pass\":\"h0ney#Mujt4b4\"}","user_type":"kiviCare_clinic_admin","clinic_id":"27","mapping_created_at":"2025-04-23 08:12:07"} 
[2025-07-22 10:12:59] local.ERROR: Failed to migrate user ID 603: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (603, suresh gant, <EMAIL>, suresh, gant, +447703175776, ?, ?, , clinic_admin, 4, 1, {"first_name":"suresh","last_name":"gant","user_email":"<EMAIL>","mobile_number":"+447703175776","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"suresh_jlvLBx","user_pass":"mmc3Jessica!"}, ?, ?, 1, system_created, 2025-07-22 10:12:59, 2025-07-22 10:12:59)) {"ID":"603","user_login":"suresh_jlvLBx","user_email":"<EMAIL>","user_nicename":"suresh_jlvlbx","display_name":"suresh gant","user_registered":"2025-04-10 09:46:12","first_name":"suresh","last_name":"gant","phone":null,"basic_data":"{\"first_name\":\"suresh\",\"last_name\":\"gant\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"+447703175776\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"suresh_jlvLBx\",\"user_pass\":\"mmc3Jessica!\"}","user_type":"kiviCare_clinic_admin","clinic_id":"26","mapping_created_at":"2025-04-10 10:46:12"} 
[2025-07-22 10:13:04] local.ERROR: Failed to migrate user ID 409: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (409, Anupam Majety, <EMAIL>, Anupam, Majety, +447466185238, ?, ?, , clinic_admin, 5, 1, {"first_name":"Anupam","last_name":"Majety","user_email":"<EMAIL>","mobile_number":"+447466185238","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Anupam_eVFdwK","user_pass":"ycdemo123"}, ?, ?, 1, system_created, 2025-07-22 10:13:04, 2025-07-22 10:13:04)) {"ID":"409","user_login":"Anupam_eVFdwK","user_email":"<EMAIL>","user_nicename":"anupam_evfdwk","display_name":"Anupam Majety","user_registered":"2025-03-06 16:03:32","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"first_name\":\"Anupam\",\"last_name\":\"Majety\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"+447466185238\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Anupam_eVFdwK\",\"user_pass\":\"ycdemo123\"}","user_type":"kiviCare_clinic_admin","clinic_id":"22","mapping_created_at":"2025-03-06 16:03:32"} 

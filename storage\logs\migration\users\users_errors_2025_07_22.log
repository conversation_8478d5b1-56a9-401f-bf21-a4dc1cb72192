[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 702: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (702, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>@doctors.net.uk, Cuong, Nguyen, 07956527607, male, ?, 5 Harmont House, 20 Harley Street, provider, 2, 1, {"mobile_number":"07956527607","gender":"male","dob":null,"address":"5 Harmont House, 20 Harley Street","city":"London","state":"","country":null,"postal_code":"W1G 9PH","npi_no":"","qualifications":[{"degree":"MBBS","university":"London","year":"1995","file":null},{"degree":"BMAcS","university":"BMAcS","year":"1997","file":null},{"degree":"FRCS","university":"RCS","year":"1998","file":null}],"price_type":null,"price":null,"no_of_experience":"0","video_price":"0","specialties":[{"id":"18","label":"General Practice"}],"time_slot":null,"gmc_no":"4259451","registration_prefix":"GMC"}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"702","user_login":"Cuong_lDWf08","user_email":"<EMAIL>","user_nicename":"cuong_ldwf08","display_name":"Cuong Nguyen","user_registered":"2025-04-25 07:36:21","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07956527607\",\"gender\":\"male\",\"dob\":null,\"address\":\"5 Harmont House, 20 Harley Street\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"npi_no\":\"\",\"qualifications\":[{\"degree\":\"MBBS\",\"university\":\"London\",\"year\":\"1995\",\"file\":null},{\"degree\":\"BMAcS\",\"university\":\"BMAcS\",\"year\":\"1997\",\"file\":null},{\"degree\":\"FRCS\",\"university\":\"RCS\",\"year\":\"1998\",\"file\":null}],\"price_type\":null,\"price\":null,\"no_of_experience\":\"0\",\"video_price\":\"0\",\"specialties\":[{\"id\":\"18\",\"label\":\"General Practice\"}],\"time_slot\":null,\"gmc_no\":\"4259451\",\"registration_prefix\":\"GMC\"}","user_type":"kiviCare_doctor","clinic_id":"40","mapping_created_at":"2025-05-07 18:44:20"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 703: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (703, Test Patient, <EMAIL>, Test, Patient, 01234567889, male, 2025-04-01 00:00:00, 5 Harmont House , 20 Harley Street, patient, 2, 1, {"mobile_number":"01234567889","gender":"male","dob":"2025-04-01","nhs":null,"address":"5 Harmont House , 20 Harley Street","city":null,"state":"","country":null,"postal_code":"W1G 9PH","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"703","user_login":"Test_Wtyku2","user_email":"<EMAIL>","user_nicename":"test_wtyku2","display_name":"Test Patient","user_registered":"2025-04-25 07:39:28","first_name":"Test","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567889\",\"gender\":\"male\",\"dob\":\"2025-04-01\",\"nhs\":null,\"address\":\"5 Harmont House , 20 Harley Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-25 08:39:28"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 727: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (727, tester Patient, <EMAIL>, tester, Patient, 01234567880, male, 1999-01-01 00:00:00, 1 road, patient, 2, 1, {"mobile_number":"01234567880","gender":"male","dob":"1999-01-01","nhs":null,"address":"1 road","city":null,"state":"","country":null,"postal_code":"q13mm","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"727","user_login":"tester_d7p2eU","user_email":"<EMAIL>","user_nicename":"tester_d7p2eu","display_name":"tester Patient","user_registered":"2025-04-30 09:50:11","first_name":"tester","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567880\",\"gender\":\"male\",\"dob\":\"1999-01-01\",\"nhs\":null,\"address\":\"1 road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"q13mm\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-30 10:50:11"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 697: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (697, Anh Nga Nguyen, <EMAIL>, Anh Nga, Nguyen, 07939155900, female, ?, , staff, 2, 1, {"mobile_number":"07939155900","gender":"female","dob":"","address":null,"city":null,"state":"","country":null,"postal_code":null}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"697","user_login":"Anh_Nga_eKwkUd","user_email":"<EMAIL>","user_nicename":"anh_nga_ekwkud","display_name":"Anh Nga Nguyen","user_registered":"2025-04-24 12:35:12","first_name":"Anh Nga","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07939155900\",\"gender\":\"female\",\"dob\":\"\",\"address\":null,\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null}","user_type":"kiviCare_receptionist","clinic_id":"40","mapping_created_at":"0000-00-00 00:00:00"} 
[2025-07-22 10:12:48] local.ERROR: Failed to migrate user ID 696: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (696, Cuong Nguyen, <EMAIL>, Cuong, Nguyen, 02076365150, ?, ?, , clinic_admin, 2, 1, {"first_name":"Cuong","last_name":"Nguyen","user_email":"<EMAIL>","mobile_number":"02076365150","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Cuong_XKlebw","user_pass":"Penthouse8!"}, ?, ?, 1, system_created, 2025-07-22 10:12:48, 2025-07-22 10:12:48)) {"ID":"696","user_login":"Cuong_XKlebw","user_email":"<EMAIL>","user_nicename":"cuong_xklebw","display_name":"Cuong Nguyen","user_registered":"2025-04-24 10:31:55","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"first_name\":\"Cuong\",\"last_name\":\"Nguyen\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"02076365150\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Cuong_XKlebw\",\"user_pass\":\"Penthouse8!\"}","user_type":"kiviCare_clinic_admin","clinic_id":"40","mapping_created_at":"2025-04-24 11:31:55"} 
[2025-07-22 10:12:53] local.ERROR: Failed to migrate user ID 678: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (678, Mujtiba Mirza, <EMAIL>, Mujtiba, Mirza, 07557642461, ?, ?, , clinic_admin, 3, 1, {"first_name":"Mujtiba","last_name":"Mirza","user_email":"<EMAIL>","mobile_number":"07557642461","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Mujtiba_5Eiv4p","user_pass":"h0ney#Mujt4b4"}, ?, ?, 1, system_created, 2025-07-22 10:12:53, 2025-07-22 10:12:53)) {"ID":"678","user_login":"Mujtiba_5Eiv4p","user_email":"<EMAIL>","user_nicename":"mujtiba_5eiv4p","display_name":"Mujtiba Mirza","user_registered":"2025-04-23 07:12:07","first_name":"Mujtiba","last_name":"Mirza","phone":null,"basic_data":"{\"first_name\":\"Mujtiba\",\"last_name\":\"Mirza\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"07557642461\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Mujtiba_5Eiv4p\",\"user_pass\":\"h0ney#Mujt4b4\"}","user_type":"kiviCare_clinic_admin","clinic_id":"27","mapping_created_at":"2025-04-23 08:12:07"} 
[2025-07-22 10:12:59] local.ERROR: Failed to migrate user ID 603: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (603, suresh gant, <EMAIL>, suresh, gant, +447703175776, ?, ?, , clinic_admin, 4, 1, {"first_name":"suresh","last_name":"gant","user_email":"<EMAIL>","mobile_number":"+447703175776","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"suresh_jlvLBx","user_pass":"mmc3Jessica!"}, ?, ?, 1, system_created, 2025-07-22 10:12:59, 2025-07-22 10:12:59)) {"ID":"603","user_login":"suresh_jlvLBx","user_email":"<EMAIL>","user_nicename":"suresh_jlvlbx","display_name":"suresh gant","user_registered":"2025-04-10 09:46:12","first_name":"suresh","last_name":"gant","phone":null,"basic_data":"{\"first_name\":\"suresh\",\"last_name\":\"gant\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"+447703175776\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"suresh_jlvLBx\",\"user_pass\":\"mmc3Jessica!\"}","user_type":"kiviCare_clinic_admin","clinic_id":"26","mapping_created_at":"2025-04-10 10:46:12"} 
[2025-07-22 10:13:04] local.ERROR: Failed to migrate user ID 409: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_basic_data' in 'field list' (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (409, Anupam Majety, <EMAIL>, Anupam, Majety, +44**********, ?, ?, , clinic_admin, 5, 1, {"first_name":"Anupam","last_name":"Majety","user_email":"<EMAIL>","mobile_number":"+44**********","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Anupam_eVFdwK","user_pass":"ycdemo123"}, ?, ?, 1, system_created, 2025-07-22 10:13:04, 2025-07-22 10:13:04)) {"ID":"409","user_login":"Anupam_eVFdwK","user_email":"<EMAIL>","user_nicename":"anupam_evfdwk","display_name":"Anupam Majety","user_registered":"2025-03-06 16:03:32","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"first_name\":\"Anupam\",\"last_name\":\"Majety\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"+44**********\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Anupam_eVFdwK\",\"user_pass\":\"ycdemo123\"}","user_type":"kiviCare_clinic_admin","clinic_id":"22","mapping_created_at":"2025-03-06 16:03:32"} 
[2025-07-22 10:17:37] local.ERROR: Failed to migrate user ID 702: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'password' cannot be null (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (702, Cuong Nguyen, <EMAIL>, Cuong, Nguyen, 07956527607, male, ?, 5 Harmont House, 20 Harley Street, provider, 2, 1, {"mobile_number":"07956527607","gender":"male","dob":null,"address":"5 Harmont House, 20 Harley Street","city":"London","state":"","country":null,"postal_code":"W1G 9PH","npi_no":"","qualifications":[{"degree":"MBBS","university":"London","year":"1995","file":null},{"degree":"BMAcS","university":"BMAcS","year":"1997","file":null},{"degree":"FRCS","university":"RCS","year":"1998","file":null}],"price_type":null,"price":null,"no_of_experience":"0","video_price":"0","specialties":[{"id":"18","label":"General Practice"}],"time_slot":null,"gmc_no":"4259451","registration_prefix":"GMC"}, ?, ?, 1, system_created, 2025-07-22 10:17:37, 2025-07-22 10:17:37)) {"ID":"702","user_login":"Cuong_lDWf08","user_email":"<EMAIL>","user_nicename":"cuong_ldwf08","display_name":"Cuong Nguyen","user_registered":"2025-04-25 07:36:21","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07956527607\",\"gender\":\"male\",\"dob\":null,\"address\":\"5 Harmont House, 20 Harley Street\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"npi_no\":\"\",\"qualifications\":[{\"degree\":\"MBBS\",\"university\":\"London\",\"year\":\"1995\",\"file\":null},{\"degree\":\"BMAcS\",\"university\":\"BMAcS\",\"year\":\"1997\",\"file\":null},{\"degree\":\"FRCS\",\"university\":\"RCS\",\"year\":\"1998\",\"file\":null}],\"price_type\":null,\"price\":null,\"no_of_experience\":\"0\",\"video_price\":\"0\",\"specialties\":[{\"id\":\"18\",\"label\":\"General Practice\"}],\"time_slot\":null,\"gmc_no\":\"4259451\",\"registration_prefix\":\"GMC\"}","user_type":"kiviCare_doctor","clinic_id":"40","mapping_created_at":"2025-05-07 18:44:20"} 
[2025-07-22 10:17:37] local.ERROR: Failed to migrate user ID 703: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'password' cannot be null (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (703, Test Patient, <EMAIL>, Test, Patient, 01234567889, male, 2025-04-01 00:00:00, 5 Harmont House , 20 Harley Street, patient, 2, 1, {"mobile_number":"01234567889","gender":"male","dob":"2025-04-01","nhs":null,"address":"5 Harmont House , 20 Harley Street","city":null,"state":"","country":null,"postal_code":"W1G 9PH","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:17:37, 2025-07-22 10:17:37)) {"ID":"703","user_login":"Test_Wtyku2","user_email":"<EMAIL>","user_nicename":"test_wtyku2","display_name":"Test Patient","user_registered":"2025-04-25 07:39:28","first_name":"Test","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567889\",\"gender\":\"male\",\"dob\":\"2025-04-01\",\"nhs\":null,\"address\":\"5 Harmont House , 20 Harley Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-25 08:39:28"} 
[2025-07-22 10:17:37] local.ERROR: Failed to migrate user ID 727: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'password' cannot be null (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (727, tester Patient, <EMAIL>, tester, Patient, 01234567880, male, 1999-01-01 00:00:00, 1 road, patient, 2, 1, {"mobile_number":"01234567880","gender":"male","dob":"1999-01-01","nhs":null,"address":"1 road","city":null,"state":"","country":null,"postal_code":"q13mm","blood_group":"","registered_gp_name":"","registered_gp_address":"","insurance_provider":"","insurance_no":""}, ?, ?, 1, system_created, 2025-07-22 10:17:37, 2025-07-22 10:17:37)) {"ID":"727","user_login":"tester_d7p2eU","user_email":"<EMAIL>","user_nicename":"tester_d7p2eu","display_name":"tester Patient","user_registered":"2025-04-30 09:50:11","first_name":"tester","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567880\",\"gender\":\"male\",\"dob\":\"1999-01-01\",\"nhs\":null,\"address\":\"1 road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"q13mm\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-30 10:50:11"} 
[2025-07-22 10:17:37] local.ERROR: Failed to migrate user ID 697: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'password' cannot be null (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (697, Anh Nga Nguyen, <EMAIL>, Anh Nga, Nguyen, 07939155900, female, ?, , staff, 2, 1, {"mobile_number":"07939155900","gender":"female","dob":"","address":null,"city":null,"state":"","country":null,"postal_code":null}, ?, ?, 1, system_created, 2025-07-22 10:17:37, 2025-07-22 10:17:37)) {"ID":"697","user_login":"Anh_Nga_eKwkUd","user_email":"<EMAIL>","user_nicename":"anh_nga_ekwkud","display_name":"Anh Nga Nguyen","user_registered":"2025-04-24 12:35:12","first_name":"Anh Nga","last_name":"Nguyen","phone":null,"basic_data":"{\"mobile_number\":\"07939155900\",\"gender\":\"female\",\"dob\":\"\",\"address\":null,\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null}","user_type":"kiviCare_receptionist","clinic_id":"40","mapping_created_at":"0000-00-00 00:00:00"} 
[2025-07-22 10:17:37] local.ERROR: Failed to migrate user ID 696: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'password' cannot be null (Connection: mysql, SQL: insert into `users` (`wp_user_id`, `name`, `email`, `first_name`, `last_name`, `phone_number`, `gender`, `date_of_birth`, `address`, `role`, `clinic_id`, `is_active`, `wp_basic_data`, `email_verified_at`, `password`, `password_change_required`, `signup_source`, `updated_at`, `created_at`) values (696, Cuong Nguyen, <EMAIL>, Cuong, Nguyen, 02076365150, ?, ?, , clinic_admin, 2, 1, {"first_name":"Cuong","last_name":"Nguyen","user_email":"<EMAIL>","mobile_number":"02076365150","gender":"","dob":"","profile_image":"","ID":"","country_code_admin":"","country_calling_code_admin":"","username":"Cuong_XKlebw","user_pass":"Penthouse8!"}, ?, ?, 1, system_created, 2025-07-22 10:17:37, 2025-07-22 10:17:37)) {"ID":"696","user_login":"Cuong_XKlebw","user_email":"<EMAIL>","user_nicename":"cuong_xklebw","display_name":"Cuong Nguyen","user_registered":"2025-04-24 10:31:55","first_name":"Cuong","last_name":"Nguyen","phone":null,"basic_data":"{\"first_name\":\"Cuong\",\"last_name\":\"Nguyen\",\"user_email\":\"<EMAIL>\",\"mobile_number\":\"02076365150\",\"gender\":\"\",\"dob\":\"\",\"profile_image\":\"\",\"ID\":\"\",\"country_code_admin\":\"\",\"country_calling_code_admin\":\"\",\"username\":\"Cuong_XKlebw\",\"user_pass\":\"Penthouse8!\"}","user_type":"kiviCare_clinic_admin","clinic_id":"40","mapping_created_at":"2025-04-24 11:31:55"} 
[2025-07-22 10:18:41] local.ERROR: Failed to create/update related records <NAME_EMAIL>: Array to string conversion (Connection: mysql, SQL: insert into `providers` (`user_id`, `specialization`, `license_number`, `verification_status`, `gender`, `pricing`, `bio`, `education`, `languages`, `accepts_insurance`, `insurance_providers`, `updated_at`, `created_at`) values (2, General Practice, GMC: 4259451, pending, male, [], Specialties: General Practice, ?, ["English"], 0, [], 2025-07-22 10:18:41, 2025-07-22 10:18:41))  
[2025-07-22 10:18:41] local.ERROR: Failed to migrate user ID 703: Session store not set on request. {"ID":"703","user_login":"Test_Wtyku2","user_email":"<EMAIL>","user_nicename":"test_wtyku2","display_name":"Test Patient","user_registered":"2025-04-25 07:39:28","first_name":"Test","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567889\",\"gender\":\"male\",\"dob\":\"2025-04-01\",\"nhs\":null,\"address\":\"5 Harmont House , 20 Harley Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"W1G 9PH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-25 08:39:28"} 
[2025-07-22 10:18:41] local.ERROR: Failed to migrate user ID 727: Session store not set on request. {"ID":"727","user_login":"tester_d7p2eU","user_email":"<EMAIL>","user_nicename":"tester_d7p2eu","display_name":"tester Patient","user_registered":"2025-04-30 09:50:11","first_name":"tester","last_name":"Patient","phone":null,"basic_data":"{\"mobile_number\":\"01234567880\",\"gender\":\"male\",\"dob\":\"1999-01-01\",\"nhs\":null,\"address\":\"1 road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"q13mm\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"40","mapping_created_at":"2025-04-30 10:50:11"} 
[2025-07-22 10:18:42] local.ERROR: Failed to create/update related records <NAME_EMAIL>: Array to string conversion (Connection: mysql, SQL: insert into `providers` (`user_id`, `specialization`, `license_number`, `verification_status`, `gender`, `pricing`, `bio`, `education`, `languages`, `accepts_insurance`, `insurance_providers`, `updated_at`, `created_at`) values (6, General Practice, , pending, ?, [], , ?, ["English"], 0, [], 2025-07-22 10:18:42, 2025-07-22 10:18:42))  
[2025-07-22 10:21:24] local.ERROR: Failed to migrate user ID 821: Session store not set on request. {"ID":"821","user_login":"Aaron_xU2E1t","user_email":"<EMAIL>","user_nicename":"aaron_xu2e1t","display_name":"Aaron Tyler","user_registered":"2025-05-21 21:43:56","first_name":"Aaron","last_name":"Tyler","phone":null,"basic_data":"{\"mobile_number\":\"07804465190\",\"dob\":\"2002-12-08\",\"nhs\":\"\",\"address\":\"Frogs island, old church road, East Hanningfield Cm38BQ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Danbury Medical Centre\",\"registered_gp_address\":\"52 Maldon Rd, Danbury, Chelmsford CM3 4QL\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-21 22:43:56"} 
[2025-07-22 10:21:24] local.ERROR: Failed to migrate user ID 803: Session store not set on request. {"ID":"803","user_login":"Abbi_EegClM","user_email":"<EMAIL>","user_nicename":"abbi_eegclm","display_name":"Abbi Lunt","user_registered":"2025-05-18 12:45:01","first_name":"Abbi","last_name":"Lunt","phone":null,"basic_data":"{\"mobile_number\":\"07854817723\",\"gender\":\"female\",\"dob\":\"1998-08-22\",\"nhs\":null,\"address\":\"9 Victoria Rd\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 6JD\",\"blood_group\":\"\",\"registered_gp_name\":\"Crouch Vale Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 13:45:01"} 
[2025-07-22 10:21:25] local.ERROR: Failed to migrate user ID 728: Session store not set on request. {"ID":"728","user_login":"Abigail_7x1try","user_email":"<EMAIL>","user_nicename":"abigail_7x1try","display_name":"Abigail Mcclymont","user_registered":"2025-04-30 12:03:54","first_name":"Abigail","last_name":"Mcclymont","phone":null,"basic_data":"{\"mobile_number\":\"07535990493\",\"gender\":\"female\",\"dob\":\"1995-11-29\",\"nhs\":null,\"address\":\"9 Badgers Close, Cm2 8qb\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"Cm2 8qb\",\"blood_group\":\"\",\"registered_gp_name\":\"Sutherland Lodge - baddow rd\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-30 13:03:54"} 
[2025-07-22 10:21:25] local.ERROR: Failed to migrate user ID 970: Session store not set on request. {"ID":"970","user_login":"Adam_ALYhXT","user_email":"<EMAIL>","user_nicename":"adam_alyhxt","display_name":"Adam Roache","user_registered":"2025-06-20 05:34:32","first_name":"Adam","last_name":"Roache","phone":null,"basic_data":"{\"mobile_number\":\"07762672765\",\"gender\":\"male\",\"dob\":\"1966-11-14\",\"nhs\":\"************\",\"address\":\"34 Mayland Close, Heybridge, Maldon Essex CM94YR\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Dr Sally Dowler\",\"registered_gp_address\":\"Longfield Medical Centre, Maldon, Essex\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-03 13:25:26"} 
[2025-07-22 10:21:25] local.ERROR: Failed to migrate user ID 472: Session store not set on request. {"ID":"472","user_login":"Adam_Bxf7Lp","user_email":"<EMAIL>","user_nicename":"adam_bxf7lp","display_name":"Adam Townsend","user_registered":"2025-03-17 18:23:08","first_name":"Adam","last_name":"Townsend","phone":null,"basic_data":"{\"mobile_number\":\"07507745911\",\"gender\":\"male\",\"dob\":\"1987-08-06\",\"nhs\":null,\"address\":\"77 Petunia Crescent, Springfield\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6YR\",\"blood_group\":\"\",\"registered_gp_name\":\"Mountbatten surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-17 18:23:08"} 
[2025-07-22 10:21:25] local.ERROR: Failed to migrate user ID 792: Session store not set on request. {"ID":"792","user_login":"Ademide_KomnFb","user_email":"<EMAIL>","user_nicename":"ademide_komnfb","display_name":"Ademide Fajobi","user_registered":"2025-05-16 19:02:41","first_name":"Ademide","last_name":"Fajobi","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2003-03-06\",\"nhs\":null,\"address\":\"15 Partridge Avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4JG\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"20, Merlin Place Chelmsford, CM1 4HW\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-17 14:19:34"} 
[2025-07-22 10:21:26] local.ERROR: Failed to migrate user ID 665: Session store not set on request. {"ID":"665","user_login":"Aimee_Mariam_YmHJCK","user_email":"<EMAIL>","user_nicename":"aimee_mariam_ymhjck","display_name":"Aimee Mariam Varghese","user_registered":"2025-04-20 13:36:40","first_name":"Aimee Mariam","last_name":"Varghese","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2014-11-28\",\"nhs\":\"**********\",\"address\":\"18 Wells Crescent, Chelmsford CM11GN\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Rivermead Gate Medical centre\",\"registered_gp_address\":\"123 Rectory Ln, CM1 1TR\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-20 14:36:40"} 
[2025-07-22 10:21:26] local.ERROR: Failed to migrate user ID 377: Session store not set on request. {"ID":"377","user_login":"Albie_19Hlzx","user_email":"<EMAIL>","user_nicename":"albie_19hlzx","display_name":"Albie Kelly","user_registered":"2025-03-01 21:41:05","first_name":"Albie","last_name":"Kelly","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1994-01-12\",\"nhs\":\"\",\"address\":\"11 tower house Brentwood Essex\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Newfolly\",\"registered_gp_address\":\"Ingatestone\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-01 21:41:05"} 
[2025-07-22 10:21:26] local.ERROR: Failed to migrate user ID 875: Session store not set on request. {"ID":"875","user_login":"Alex_DZ3dpW","user_email":"<EMAIL>","user_nicename":"alex_dz3dpw","display_name":"Alex Wisbey","user_registered":"2025-06-02 17:18:52","first_name":"Alex","last_name":"Wisbey","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1984-11-02\",\"nhs\":\"\",\"address\":\"36 Barn Green Springfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Chelmsford medical centre\",\"registered_gp_address\":\"36 Barn Green\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-02 18:18:52"} 
[2025-07-22 10:21:26] local.ERROR: Failed to migrate user ID 446: Session store not set on request. {"ID":"446","user_login":"Alfie_usQWRj","user_email":"<EMAIL>","user_nicename":"alfie_usqwrj","display_name":"Alfie Bloomfield","user_registered":"2025-03-13 13:34:47","first_name":"Alfie","last_name":"Bloomfield","phone":null,"basic_data":"{\"mobile_number\":\"07123456789\",\"gender\":\"male\",\"dob\":\"2002-06-16\",\"nhs\":null,\"address\":\"17 4th Avenue\",\"city\":\"Chelsmford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4EZ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-13 13:34:47"} 
[2025-07-22 10:21:27] local.ERROR: Failed to migrate user ID 840: Session store not set on request. {"ID":"840","user_login":"Alisha_z0ZrHy","user_email":"<EMAIL>","user_nicename":"alisha_z0zrhy","display_name":"Alisha Gent","user_registered":"2025-05-26 12:40:45","first_name":"Alisha","last_name":"Gent","phone":null,"basic_data":"{\"mobile_number\":\"07885496732\",\"gender\":\"female\",\"dob\":\"1999-04-07\",\"nhs\":null,\"address\":\"Anvil House, Hollow Road\",\"city\":\"Felsted\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 3JF\",\"blood_group\":\"\",\"registered_gp_name\":\"Felsted Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-26 13:40:45"} 
[2025-07-22 10:21:27] local.ERROR: Failed to migrate user ID 749: Session store not set on request. {"ID":"749","user_login":"Alison_kSzOZC","user_email":"<EMAIL>","user_nicename":"alison_kszozc","display_name":"Alison Garlick","user_registered":"2025-05-06 16:22:46","first_name":"Alison","last_name":"Garlick","phone":null,"basic_data":"{\"mobile_number\":\"07779262488\",\"dob\":\"1980-09-28\",\"nhs\":\"\",\"address\":\"9 lake mead, Heybridge, Maldon, CM9 4UJ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Abeyratna\",\"registered_gp_address\":\"Blackwater medical centre, princes road, maldon\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-06 17:22:46"} 
[2025-07-22 10:21:27] local.ERROR: Failed to migrate user ID 762: Session store not set on request. {"ID":"762","user_login":"Alison_DZUfT3","user_email":"<EMAIL>","user_nicename":"alison_dzuft3","display_name":"Alison White","user_registered":"2025-05-10 10:07:03","first_name":"Alison","last_name":"White","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1970-12-04\",\"nhs\":\"************\",\"address\":\"25 South Weald Road Brentwood CM14 4QZ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Tania Burgess\",\"registered_gp_address\":\"New Surgery, 8 Shenfield Road, CM15 8AB\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-10 11:07:03"} 
[2025-07-22 10:21:27] local.ERROR: Failed to migrate user ID 323: Session store not set on request. {"ID":"323","user_login":"Allan_QWCAxS","user_email":"<EMAIL>","user_nicename":"allan_qwcaxs","display_name":"Allan FEARIS","user_registered":"2025-02-17 10:05:13","first_name":"Allan","last_name":"FEARIS","phone":null,"basic_data":"{\"mobile_number\":\"07767146639\",\"gender\":\"male\",\"dob\":\"1957-10-05\",\"nhs\":\"**********\",\"address\":\"49 REMBRANDT GROVE CHELMSFORD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6GD\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-17 10:44:22"} 
[2025-07-22 10:21:27] local.ERROR: Failed to migrate user ID 772: Session store not set on request. {"ID":"772","user_login":"Alwyn_AFuf6j","user_email":"<EMAIL>","user_nicename":"alwyn_afuf6j","display_name":"Alwyn levican","user_registered":"2025-05-12 09:13:44","first_name":"Alwyn","last_name":"levican","phone":null,"basic_data":"{\"mobile_number\":\"07939599640\",\"gender\":\"male\",\"dob\":\"1981-07-14\",\"nhs\":null,\"address\":\"37 Nelmes creacent\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"RM112PX\",\"blood_group\":\"\",\"registered_gp_name\":\"Greenwood\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-16 10:35:54"} 
[2025-07-22 10:21:28] local.ERROR: Failed to migrate user ID 470: Session store not set on request. {"ID":"470","user_login":"Amanda_WwpCUl","user_email":"<EMAIL>","user_nicename":"amanda_wwpcul","display_name":"Amanda Munro","user_registered":"2025-03-17 10:20:05","first_name":"Amanda","last_name":"Munro","phone":null,"basic_data":"{\"mobile_number\":\"07521780283\",\"gender\":\"female\",\"dob\":\"1991-06-24\",\"nhs\":null,\"address\":\"50 Snowdrop close, chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM16XD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-18 10:09:18"} 
[2025-07-22 10:21:28] local.ERROR: Failed to migrate user ID 939: Session store not set on request. {"ID":"939","user_login":"Amelie_doCJ4B","user_email":"<EMAIL>","user_nicename":"amelie_docj4b","display_name":"Amelie Bradshaw","user_registered":"2025-06-15 11:18:58","first_name":"Amelie","last_name":"Bradshaw","phone":null,"basic_data":"{\"mobile_number\":\"07464292527\",\"gender\":\"female\",\"dob\":\"2006-10-17\",\"nhs\":null,\"address\":\"Yeomans, Rolphy Green, Pleshey\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1JQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-15 12:18:58"} 
[2025-07-22 10:21:28] local.ERROR: Failed to migrate user ID 923: Session store not set on request. {"ID":"923","user_login":"Amie_wY02QW","user_email":"<EMAIL>","user_nicename":"amie_wy02qw","display_name":"Amie Johnson","user_registered":"2025-06-12 16:47:11","first_name":"Amie","last_name":"Johnson","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"female\",\"dob\":\"1994-03-24\",\"nhs\":null,\"address\":\"35 Wilfred Waterman Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6AZ\",\"blood_group\":\"\",\"registered_gp_name\":\"North Chelmford\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-12 17:47:11"} 
[2025-07-22 10:21:28] local.ERROR: Failed to migrate user ID 955: Session store not set on request. {"ID":"955","user_login":"Amie_vgw29a","user_email":"<EMAIL>","user_nicename":"amie_vgw29a","display_name":"Amie Johnson","user_registered":"2025-06-17 20:10:33","first_name":"Amie","last_name":"Johnson","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1994-03-24\",\"nhs\":\"\",\"address\":\"35 Wilfred waterman drive Chelmsford Essex Cm1 6az\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"North Chelmsford NHS\",\"registered_gp_address\":\"2 white hart lane Springfield Chelmsford CM2 5EF\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-17 21:10:33"} 
[2025-07-22 10:21:29] local.ERROR: Failed to migrate user ID 658: Session store not set on request. {"ID":"658","user_login":"Amy_Qfyqn8","user_email":"<EMAIL>","user_nicename":"amy_qfyqn8","display_name":"Amy Poyntz","user_registered":"2025-04-19 09:06:48","first_name":"Amy","last_name":"Poyntz","phone":null,"basic_data":"{\"mobile_number\":\"07365528538\",\"gender\":\"female\",\"dob\":\"2002-11-02\",\"nhs\":null,\"address\":\"27 Corbeck Rd\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 1FY\",\"blood_group\":\"\",\"registered_gp_name\":\"EDEN sURGERY , Hatfield Heath\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-19 10:06:48"} 
[2025-07-22 10:21:29] local.ERROR: Failed to migrate user ID 977: Session store not set on request. {"ID":"977","user_login":"Amy_cYyL15","user_email":"<EMAIL>","user_nicename":"amy_cyyl15","display_name":"Amy Stannard","user_registered":"2025-06-21 10:47:50","first_name":"Amy","last_name":"Stannard","phone":null,"basic_data":"{\"mobile_number\":\"07535402909\",\"dob\":\"1998-02-21\",\"nhs\":\"\",\"address\":\"Attridges farm Rands road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Angel lane\",\"registered_gp_address\":\"Great fun is\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-21 11:47:50"} 
[2025-07-22 10:21:29] local.ERROR: Failed to migrate user ID 537: Session store not set on request. {"ID":"537","user_login":"Anarkha_Manju_eFj7th","user_email":"<EMAIL>","user_nicename":"anarkha_manju_efj7th","display_name":"Anarkha Manju Sabu","user_registered":"2025-03-27 15:46:43","first_name":"Anarkha Manju","last_name":"Sabu","phone":null,"basic_data":"{\"mobile_number\":\"07384063456\",\"gender\":\"female\",\"dob\":\"2001-05-14\",\"nhs\":null,\"address\":\"38, Darnay Rise, Chelmsford, CM14XA\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm14xa\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-27 15:46:43"} 
[2025-07-22 10:21:29] local.ERROR: Failed to migrate user ID 710: Session store not set on request. {"ID":"710","user_login":"Andras_qQ1iFA","user_email":"<EMAIL>","user_nicename":"andras_qq1ifa","display_name":"Andras Mede","user_registered":"2025-04-26 08:23:56","first_name":"Andras","last_name":"Mede","phone":null,"basic_data":"{\"mobile_number\":\"07399032033\",\"dob\":\"1984-10-20\",\"nhs\":\"\",\"address\":\"18.Hollis LOCK\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-26 09:23:56"} 
[2025-07-22 10:21:29] local.ERROR: Failed to migrate user ID 661: Session store not set on request. {"ID":"661","user_login":"Andrew_4opr1u","user_email":"<EMAIL>","user_nicename":"andrew_4opr1u","display_name":"Andrew Boyle","user_registered":"2025-04-19 13:25:21","first_name":"Andrew","last_name":"Boyle","phone":null,"basic_data":"{\"mobile_number\":\"07740298283\",\"gender\":\"male\",\"dob\":\"2049-07-07\",\"nhs\":null,\"address\":\"37 Sunningdale Road, CM1 2NH\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2NH\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-19 14:25:21"} 
[2025-07-22 10:21:30] local.ERROR: Failed to migrate user ID 626: Session store not set on request. {"ID":"626","user_login":"Andrew_URKBub","user_email":"<EMAIL>","user_nicename":"andrew_urkbub","display_name":"Andrew Grant","user_registered":"2025-04-13 09:10:20","first_name":"Andrew","last_name":"Grant","phone":null,"basic_data":"{\"mobile_number\":\"07742690915\",\"gender\":\"male\",\"dob\":\"1965-06-01\",\"nhs\":null,\"address\":\"24 Foxglove Ave\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4FW\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 10:10:20"} 
[2025-07-22 10:21:30] local.ERROR: Failed to migrate user ID 874: Session store not set on request. {"ID":"874","user_login":"Andrew_cH6bXK","user_email":"<EMAIL>","user_nicename":"andrew_ch6bxk","display_name":"Andrew Newborough","user_registered":"2025-06-02 14:06:48","first_name":"Andrew","last_name":"Newborough","phone":null,"basic_data":"{\"mobile_number\":\"07976310585\",\"dob\":\"1967-11-19\",\"nhs\":\"\",\"address\":\"75 Chelsea Gardens, Harlow, Essex, CM17 9RY\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Hamilton Practice\",\"registered_gp_address\":\"Bush Fair, Harlow\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-02 15:06:48"} 
[2025-07-22 10:21:30] local.ERROR: Failed to migrate user ID 270: Session store not set on request. {"ID":"270","user_login":"Angela-Jane_bWu4pH","user_email":"<EMAIL>","user_nicename":"angela-jane_bwu4ph","display_name":"Angela-Jane Jarvis","user_registered":"2025-02-09 10:03:36","first_name":"Angela-Jane","last_name":"Jarvis","phone":null,"basic_data":"{\"mobile_number\":\"07918140965\",\"gender\":\"female\",\"dob\":\"1966-05-17\",\"nhs\":null,\"address\":\"2 Royal Cottages, Shalford Rd\",\"city\":\"Braintree\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM77 6DF\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 10:03:36"} 
[2025-07-22 10:21:30] local.ERROR: Failed to migrate user ID 660: Session store not set on request. {"ID":"660","user_login":"Angie_5veSN7","user_email":"<EMAIL>","user_nicename":"angie_5vesn7","display_name":"Angie Middleton","user_registered":"2025-04-19 11:38:04","first_name":"Angie","last_name":"Middleton","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1976-10-24\",\"nhs\":\"**********\",\"address\":\"8 milbank, cm2 6yx\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"North Springfield doctors\",\"registered_gp_address\":\"White hart lane, cm2 5ef\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-01 18:26:53"} 
[2025-07-22 10:21:31] local.ERROR: Failed to migrate user ID 964: Session store not set on request. {"ID":"964","user_login":"Ann_SxYghF","user_email":"<EMAIL>","user_nicename":"ann_sxyghf","display_name":"Ann Matthews","user_registered":"2025-06-19 10:12:39","first_name":"Ann","last_name":"Matthews","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1956-12-23\",\"nhs\":\"**********\",\"address\":\"4 Nicholas Court Nickleby Road Chelmsford, CM1 4XE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"Parkside Medical Centre, Melbourne Avenue, Chelmsford, CM1 2DY\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-20 11:03:52"} 
[2025-07-22 10:21:31] local.ERROR: Failed to migrate user ID 1002: Session store not set on request. {"ID":"1002","user_login":"Ann_oZt0iK","user_email":"<EMAIL>","user_nicename":"ann_ozt0ik","display_name":"Ann Morrell","user_registered":"2025-06-28 09:10:11","first_name":"Ann","last_name":"Morrell","phone":null,"basic_data":"{\"mobile_number\":\"01279871965\",\"gender\":\"female\",\"dob\":\"1947-05-11\",\"nhs\":null,\"address\":\"The Laurels, Warren Close\",\"city\":\"Takeley\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM22 6QD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-03 12:55:54"} 
[2025-07-22 10:21:31] local.ERROR: Failed to migrate user ID 1020: Session store not set on request. {"ID":"1020","user_login":"Ann_wjKSB8","user_email":"<EMAIL>","user_nicename":"ann_wjksb8","display_name":"Ann Robey","user_registered":"2025-07-02 16:06:32","first_name":"Ann","last_name":"Robey","phone":null,"basic_data":"{\"mobile_number\":\"07812736263\",\"gender\":\"female\",\"dob\":\"1960-03-20\",\"nhs\":null,\"address\":\"St. Michael's Cottage, The Street, Pleshey, Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1HA\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham + Great Notley Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-02 17:06:32"} 
[2025-07-22 10:21:31] local.ERROR: Failed to migrate user ID 503: Session store not set on request. {"ID":"503","user_login":"Anne_3Kun0t","user_email":"<EMAIL>","user_nicename":"anne_3kun0t","display_name":"Anne Quinton","user_registered":"2025-03-23 11:43:41","first_name":"Anne","last_name":"Quinton","phone":null,"basic_data":"{\"mobile_number\":\"07526432638\",\"gender\":\"female\",\"dob\":\"1943-01-22\",\"nhs\":null,\"address\":\"14 Burnhole Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 7HD\",\"blood_group\":\"\",\"registered_gp_name\":\"Brooks Surgery\",\"registered_gp_address\":\"Little Waltham\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 11:43:41"} 
[2025-07-22 10:21:32] local.ERROR: Failed to migrate user ID 574: Session store not set on request. {"ID":"574","user_login":"Anthony_nqHyV4","user_email":"<EMAIL>","user_nicename":"anthony_nqhyv4","display_name":"Anthony Copleston","user_registered":"2025-04-05 12:39:56","first_name":"Anthony","last_name":"Copleston","phone":null,"basic_data":"{\"mobile_number\":\"07869174119\",\"gender\":\"male\",\"dob\":\"1959-11-07\",\"nhs\":null,\"address\":\"26 Washington Road, Maldon, Essex , CM9 6BL\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 6BL\",\"blood_group\":\"\",\"registered_gp_name\":\"Longfields NHS GP Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-05 13:39:56"} 
[2025-07-22 10:21:32] local.ERROR: Failed to migrate user ID 932: Session store not set on request. {"ID":"932","user_login":"Anthony_OHwpDS","user_email":"<EMAIL>","user_nicename":"anthony_ohwpds","display_name":"Anthony Guest","user_registered":"2025-06-14 09:33:09","first_name":"Anthony","last_name":"Guest","phone":null,"basic_data":"{\"mobile_number\":\"07753994236\",\"gender\":\"male\",\"dob\":\"1949-02-05\",\"nhs\":null,\"address\":\"173 Main Road\",\"city\":\"Great Leighs\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1NP\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-14 10:33:09"} 
[2025-07-22 10:21:32] local.ERROR: Failed to migrate user ID 367: Session store not set on request. {"ID":"367","user_login":"Antzela_O709j6","user_email":"<EMAIL>","user_nicename":"antzela_o709j6","display_name":"Antzela Zoumei","user_registered":"2025-02-27 17:33:32","first_name":"Antzela","last_name":"Zoumei","phone":null,"basic_data":"{\"mobile_number\":\"07723591299\",\"gender\":\"female\",\"dob\":\"1996-06-06\",\"nhs\":null,\"address\":\"11 Rivers House Chelmsford CM26JL\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM26JL\",\"blood_group\":\"\",\"registered_gp_name\":\"Wood green\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-27 17:33:32"} 
[2025-07-22 10:21:32] local.ERROR: Failed to migrate user ID 183: Session store not set on request. {"ID":"183","user_login":"Anupam_EwW1BY","user_email":"<EMAIL>","user_nicename":"anupam_eww1by","display_name":"Anupam Majety","user_registered":"2025-01-15 16:03:17","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1994-10-21\",\"nhs\":null,\"address\":\"50 Spital Lane\",\"city\":\"London\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM14 5PG\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-15 16:03:17"} 
[2025-07-22 10:21:33] local.ERROR: Failed to migrate user ID 191: Session store not set on request. {"ID":"191","user_login":"Anupam_H7AaLp","user_email":"<EMAIL>","user_nicename":"anupam_h7aalp","display_name":"Anupam Majety","user_registered":"2025-01-20 11:06:50","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1111-11-11\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-20 11:06:50"} 
[2025-07-22 10:21:33] local.ERROR: Failed to migrate user ID 199: Session store not set on request. {"ID":"199","user_login":"Anupam_0k1JfU","user_email":"<EMAIL>","user_nicename":"anupam_0k1jfu","display_name":"Anupam Majety","user_registered":"2025-01-20 16:49:29","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1111-11-11\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-20 16:49:30"} 
[2025-07-22 10:21:33] local.ERROR: Failed to migrate user ID 243: Session store not set on request. {"ID":"243","user_login":"Anupam_2xjbkY","user_email":"<EMAIL>","user_nicename":"anupam_2xjbky","display_name":"Anupam Majety","user_registered":"2025-02-02 11:40:06","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1212-12-12\",\"nhs\":\"757945878\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-02 11:40:06"} 
[2025-07-22 10:21:33] local.ERROR: Failed to migrate user ID 273: Session store not set on request. {"ID":"273","user_login":"Anupam_cfyOqA","user_email":"<EMAIL>","user_nicename":"anupam_cfyoqa","display_name":"Anupam Majety","user_registered":"2025-02-09 10:13:11","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-12\",\"nhs\":\"62528\",\"address\":\"Test test\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"Tert\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 10:13:11"} 
[2025-07-22 10:21:33] local.ERROR: Failed to migrate user ID 274: Session store not set on request. {"ID":"274","user_login":"Anupam_UVe2TP","user_email":"<EMAIL>","user_nicename":"anupam_uve2tp","display_name":"Anupam Majety","user_registered":"2025-02-09 10:30:20","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-14\",\"nhs\":\"TEST\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 10:30:20"} 
[2025-07-22 10:21:34] local.ERROR: Failed to migrate user ID 281: Session store not set on request. {"ID":"281","user_login":"Anupam_iJm5Dv","user_email":"<EMAIL>","user_nicename":"anupam_ijm5dv","display_name":"Anupam Majety","user_registered":"2025-02-10 12:30:16","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-14\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-10 12:30:16"} 
[2025-07-22 10:21:34] local.ERROR: Failed to migrate user ID 290: Session store not set on request. {"ID":"290","user_login":"Anupam_QMmSVc","user_email":"<EMAIL>","user_nicename":"anupam_qmmsvc","display_name":"Anupam Majety","user_registered":"2025-02-11 09:20:20","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-01\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-11 09:20:20"} 
[2025-07-22 10:21:34] local.ERROR: Failed to migrate user ID 347: Session store not set on request. {"ID":"347","user_login":"Anupam_Qdpeqx","user_email":"majetyanupam+_878^<EMAIL>","user_nicename":"anupam_qdpeqx","display_name":"Anupam Majety","user_registered":"2025-02-25 10:38:14","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-26\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM14 5PG\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-25 10:38:15"} 
[2025-07-22 10:21:35] local.ERROR: Failed to migrate user ID 556: Session store not set on request. {"ID":"556","user_login":"Anupam_UJTizS","user_email":"<EMAIL>","user_nicename":"anupam_ujtizs","display_name":"Anupam Majety","user_registered":"2025-03-31 11:35:05","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-03-20\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-31 12:35:05"} 
[2025-07-22 10:21:35] local.ERROR: Failed to migrate user ID 571: Session store not set on request. {"ID":"571","user_login":"Anupam_F2DO1s","user_email":"<EMAIL>","user_nicename":"anupam_f2do1s","display_name":"Anupam Majety","user_registered":"2025-04-05 12:03:12","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1776-04-03\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-05 13:03:12"} 
[2025-07-22 10:21:35] local.ERROR: Failed to migrate user ID 572: Session store not set on request. {"ID":"572","user_login":"Anupam_SbszHC","user_email":"<EMAIL>","user_nicename":"anupam_sbszhc","display_name":"Anupam Majety","user_registered":"2025-04-05 12:15:36","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-06\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"sdvs\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-05 13:15:36"} 
[2025-07-22 10:21:35] local.ERROR: Failed to migrate user ID 593: Session store not set on request. {"ID":"593","user_login":"Anupam_NsLPD6","user_email":"<EMAIL>","user_nicename":"anupam_nslpd6","display_name":"Anupam Majety","user_registered":"2025-04-08 11:22:41","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-12\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-08 12:22:41"} 
[2025-07-22 10:21:35] local.ERROR: Failed to migrate user ID 594: Session store not set on request. {"ID":"594","user_login":"Anupam_EgN4QV","user_email":"<EMAIL>","user_nicename":"anupam_egn4qv","display_name":"Anupam Majety","user_registered":"2025-04-08 11:28:23","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-20\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-08 12:28:23"} 
[2025-07-22 10:21:36] local.ERROR: Failed to migrate user ID 612: Session store not set on request. {"ID":"612","user_login":"Anupam_uI2cy1","user_email":"<EMAIL>","user_nicename":"anupam_ui2cy1","display_name":"Anupam Majety","user_registered":"2025-04-11 16:23:48","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-11\",\"nhs\":\"7575NH\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 17:23:48"} 
[2025-07-22 10:21:36] local.ERROR: Failed to migrate user ID 735: Session store not set on request. {"ID":"735","user_login":"Anupam_dwONZX","user_email":"<EMAIL>","user_nicename":"anupam_dwonzx","display_name":"Anupam Majety","user_registered":"2025-05-03 12:01:00","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"2025-05-09\",\"nhs\":\"\",\"address\":\"Trdfgb\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Gvbvc\",\"registered_gp_address\":\"Dgffhb\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-03 13:01:00"} 
[2025-07-22 10:21:36] local.ERROR: Failed to migrate user ID 779: Session store not set on request. {"ID":"779","user_login":"Anupam_2EaSUW","user_email":"<EMAIL>","user_nicename":"anupam_2easuw","display_name":"Anupam Majety","user_registered":"2025-05-13 14:10:08","first_name":"Anupam","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-05-24\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-13 15:10:08"} 
[2025-07-22 10:21:36] local.ERROR: Failed to migrate user ID 595: Session store not set on request. {"ID":"595","user_login":"Anupam_Raja_Ashwin_spfXzt","user_email":"<EMAIL>","user_nicename":"anupam_raja_ashwin_spfxzt","display_name":"Anupam Raja Ashwin Majety","user_registered":"2025-04-08 17:20:29","first_name":"Anupam Raja Ashwin","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-02\",\"nhs\":\"\",\"address\":\"62 Campus Avenue\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"sdvas\",\"registered_gp_address\":\"62 Campus Avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-08 18:20:29"} 
[2025-07-22 10:21:37] local.ERROR: Failed to migrate user ID 616: Session store not set on request. {"ID":"616","user_login":"Anupam_Raja_Ashwin_6JSm0z","user_email":"<EMAIL>","user_nicename":"anupam_raja_ashwin_6jsm0z","display_name":"Anupam Raja Ashwin Majety","user_registered":"2025-04-11 18:13:45","first_name":"Anupam Raja Ashwin","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-04\",\"nhs\":\"\",\"address\":\"62 Campus Avenue\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"dd\",\"registered_gp_address\":\"62 Campus Avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 19:13:45"} 
[2025-07-22 10:21:37] local.ERROR: Failed to migrate user ID 487: Session store not set on request. {"ID":"487","user_login":"Anupam_whnQgH","user_email":"<EMAIL>","user_nicename":"anupam_whnqgh","display_name":"Anupam1 Majety","user_registered":"2025-03-21 10:01:05","first_name":"Anupam1","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2025-03-31\",\"nhs\":null,\"address\":\"50 Spital Lane\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Test GP\",\"registered_gp_address\":\"50 Spital Lane, GP\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-30 15:48:23"} 
[2025-07-22 10:21:37] local.ERROR: Failed to migrate user ID 654: Session store not set on request. {"ID":"654","user_login":"April_bKROnZ","user_email":"<EMAIL>","user_nicename":"april_bkronz","display_name":"April Flanagan","user_registered":"2025-04-18 11:42:54","first_name":"April","last_name":"Flanagan","phone":null,"basic_data":"{\"mobile_number\":\"07892714093\",\"gender\":\"female\",\"dob\":\"1993-04-16\",\"nhs\":null,\"address\":\"77 Highcliffe Road\",\"city\":\"Wickford\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS11 8JP\",\"blood_group\":\"\",\"registered_gp_name\":\"Robert Frew\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 12:42:54"} 
[2025-07-22 10:21:37] local.ERROR: Failed to migrate user ID 739: Session store not set on request. {"ID":"739","user_login":"Archie_OQsuWM","user_email":"<EMAIL>","user_nicename":"archie_oqsuwm","display_name":"Archie Cobb","user_registered":"2025-05-04 10:12:34","first_name":"Archie","last_name":"Cobb","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2024-09-21\",\"nhs\":\"**********\",\"address\":\"4 river view, SS12 0FE\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Applewood\",\"registered_gp_address\":\"2 Market Road, Wickford, Essex, SS12 0AG\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-04 11:12:34"} 
[2025-07-22 10:21:38] local.ERROR: Failed to migrate user ID 436: Session store not set on request. {"ID":"436","user_login":"Aron_vI60jA","user_email":"<EMAIL>","user_nicename":"aron_vi60ja","display_name":"Aron Sutherson","user_registered":"2025-03-11 18:08:21","first_name":"Aron","last_name":"Sutherson","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2007-10-23\",\"nhs\":\"\",\"address\":\"66 regiment gate Cm1 6bq\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"North Chelmsford health centre\",\"registered_gp_address\":\"2 Whitehart lane,cm2 5ef\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-11 18:08:21"} 
[2025-07-22 10:21:38] local.ERROR: Failed to migrate user ID 714: Session store not set on request. {"ID":"714","user_login":"Arran_jsHgYk","user_email":"<EMAIL>","user_nicename":"arran_jshgyk","display_name":"Arran Flanders","user_registered":"2025-04-27 07:42:31","first_name":"Arran","last_name":"Flanders","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1991-09-15\",\"nhs\":\"\",\"address\":\"57 Baynard Avenue, Flitch Green, Dunmow, CM6 3FF\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Lyn\",\"registered_gp_address\":\"Angel lane surgery, Dunmow, cm6 1aq\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-27 08:42:31"} 
[2025-07-22 10:21:38] local.ERROR: Failed to migrate user ID 558: Session store not set on request. {"ID":"558","user_login":"Arthur_64QBrh","user_email":"<EMAIL>","user_nicename":"arthur_64qbrh","display_name":"Arthur Ericson","user_registered":"2025-04-01 11:25:50","first_name":"Arthur","last_name":"Ericson","phone":null,"basic_data":"{\"mobile_number\":\"07586456661\",\"gender\":\"male\",\"dob\":\"1948-12-07\",\"nhs\":null,\"address\":\"The Barn\",\"city\":\"Billericay\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"cm11\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-02 11:22:45"} 
[2025-07-22 10:21:38] local.ERROR: Failed to migrate user ID 628: Session store not set on request. {"ID":"628","user_login":"Arthur_John_VsEjMI","user_email":"<EMAIL>","user_nicename":"arthur_john_vsejmi","display_name":"Arthur John Curtis","user_registered":"2025-04-13 10:46:51","first_name":"Arthur John","last_name":"Curtis","phone":null,"basic_data":"{\"mobile_number\":\"07500661161\",\"gender\":\"male\",\"dob\":\"2023-11-12\",\"nhs\":null,\"address\":\"10 Dorset Way\",\"city\":\"Billericay\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM12 0UD\",\"blood_group\":\"\",\"registered_gp_name\":\"Western Road Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 11:46:51"} 
[2025-07-22 10:21:39] local.ERROR: Failed to migrate user ID 280: Session store not set on request. {"ID":"280","user_login":"Ashwin_m3Azv5","user_email":"<EMAIL>","user_nicename":"ashwin_m3azv5","display_name":"Ashwin Anipam","user_registered":"2025-02-10 12:05:11","first_name":"Ashwin","last_name":"Anipam","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-13\",\"nhs\":\"Test\",\"address\":\"Test address\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"Cm145pg\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-10 12:05:11"} 
[2025-07-22 10:21:39] local.ERROR: Failed to migrate user ID 480: Session store not set on request. {"ID":"480","user_login":"Audit_Test_2viU53","user_email":"<EMAIL>","user_nicename":"audit_test_2viu53","display_name":"Audit Test Test","user_registered":"2025-03-19 12:26:08","first_name":"Audit Test","last_name":"Test","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"2025-03-05\",\"nhs\":null,\"address\":\"50 Spital Lane\",\"city\":\"London\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM14 5PG\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"50 Spital Lane\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-19 12:26:08"} 
[2025-07-22 10:21:39] local.ERROR: Failed to migrate user ID 798: Session store not set on request. {"ID":"798","user_login":"Avril_q7WEHs","user_email":"<EMAIL>","user_nicename":"avril_q7wehs","display_name":"Avril Garrett","user_registered":"2025-05-18 07:16:50","first_name":"Avril","last_name":"Garrett","phone":null,"basic_data":"{\"mobile_number\":\"07825999397\",\"dob\":\"1939-09-22\",\"nhs\":\"************\",\"address\":\"8 Willow Close Broomfield Chelmsford CM1 7AY\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Agarwal\",\"registered_gp_address\":\"Little Waltham Surgery, Brook Hill, Little Waltham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 08:16:51"} 
[2025-07-22 10:21:39] local.ERROR: Failed to migrate user ID 331: Session store not set on request. {"ID":"331","user_login":"Barbara_NX2FEe","user_email":"<EMAIL>","user_nicename":"barbara_nx2fee","display_name":"Barbara Blanks","user_registered":"2025-02-20 11:37:07","first_name":"Barbara","last_name":"Blanks","phone":null,"basic_data":"{\"mobile_number\":\"01234567893\",\"gender\":\"female\",\"dob\":\"1954-05-18\",\"nhs\":null,\"address\":\"11 Carstone Place CM14YT\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-20 11:37:07"} 
[2025-07-22 10:21:40] local.ERROR: Failed to migrate user ID 910: Session store not set on request. {"ID":"910","user_login":"Barry_wWTbpP","user_email":"<EMAIL>","user_nicename":"barry_wwtbpp","display_name":"Barry Coyle","user_registered":"2025-06-10 09:18:19","first_name":"Barry","last_name":"Coyle","phone":null,"basic_data":"{\"mobile_number\":\"07787568246\",\"gender\":\"male\",\"dob\":\"1942-01-09\",\"nhs\":null,\"address\":\"3 woodhouse lane, Broomfield\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4UU\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-10 10:18:19"} 
[2025-07-22 10:21:40] local.ERROR: Failed to migrate user ID 611: Session store not set on request. {"ID":"611","user_login":"Ben_3U5JNo","user_email":"<EMAIL>","user_nicename":"ben_3u5jno","display_name":"Ben Parks","user_registered":"2025-04-11 16:13:30","first_name":"Ben","last_name":"Parks","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"1989-01-11\",\"nhs\":null,\"address\":\"1 Sunningdale Road, Chlemsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12NH\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 17:13:30"} 
[2025-07-22 10:21:40] local.ERROR: Failed to migrate user ID 881: Session store not set on request. {"ID":"881","user_login":"Benjamin_rYO9SK","user_email":"<EMAIL>","user_nicename":"benjamin_ryo9sk","display_name":"Benjamin Cavanagh","user_registered":"2025-06-03 14:24:16","first_name":"Benjamin","last_name":"Cavanagh","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1976-08-16\",\"nhs\":null,\"address\":\"26 Buckingham Court, The Close Dunmow CM6 1XE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"The Village Surgey\",\"registered_gp_address\":\"Harpenden\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-03 16:03:31"} 
[2025-07-22 10:21:40] local.ERROR: Failed to migrate user ID 505: Session store not set on request. {"ID":"505","user_login":"Benjamin_xGcpKd","user_email":"<EMAIL>","user_nicename":"benjamin_xgcpkd","display_name":"Benjamin Erikson","user_registered":"2025-03-23 15:04:04","first_name":"Benjamin","last_name":"Erikson","phone":null,"basic_data":"{\"mobile_number\":\"07713951903\",\"gender\":\"male\",\"dob\":\"2004-02-04\",\"nhs\":null,\"address\":\"63 Lady Lane\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM2 0TQ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 17:00:59"} 
[2025-07-22 10:21:40] local.ERROR: Failed to migrate user ID 630: Session store not set on request. {"ID":"630","user_login":"Benjamin_9BqgfG","user_email":"<EMAIL>","user_nicename":"benjamin_9bqgfg","display_name":"Benjamin Mabhena","user_registered":"2025-04-13 13:27:25","first_name":"Benjamin","last_name":"Mabhena","phone":null,"basic_data":"{\"mobile_number\":\"07445240564\",\"gender\":\"male\",\"dob\":\"1980-03-17\",\"nhs\":null,\"address\":\"29a Hunts Dirve\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3HQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 14:27:25"} 
[2025-07-22 10:21:41] local.ERROR: Failed to migrate user ID 608: Session store not set on request. {"ID":"608","user_login":"Benjamin_ijmpDG","user_email":"<EMAIL>","user_nicename":"benjamin_ijmpdg","display_name":"Benjamin Parks","user_registered":"2025-04-11 07:55:30","first_name":"Benjamin","last_name":"Parks","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-11\",\"nhs\":\"**********\",\"address\":\"1 Sunningdale Road Chelmsford CM1 2NH\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"Melbourne House Surgery, CM1 2DY\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 08:55:30"} 
[2025-07-22 10:21:41] local.ERROR: Failed to migrate user ID 759: Session store not set on request. {"ID":"759","user_login":"Bernard_eWmgFM","user_email":"<EMAIL>","user_nicename":"bernard_ewmgfm","display_name":"Bernard Simmons","user_registered":"2025-05-09 10:59:33","first_name":"Bernard","last_name":"Simmons","phone":null,"basic_data":"{\"mobile_number\":\"07596762191\",\"gender\":\"male\",\"dob\":\"1943-02-04\",\"nhs\":null,\"address\":\"19 Dombey Close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14YF\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-09 11:59:33"} 
[2025-07-22 10:21:41] local.ERROR: Failed to migrate user ID 302: Session store not set on request. {"ID":"302","user_login":"Beverley_KSXJYm","user_email":"<EMAIL>","user_nicename":"beverley_ksxjym","display_name":"Beverley Eckhart","user_registered":"2025-02-13 11:05:47","first_name":"Beverley","last_name":"Eckhart","phone":null,"basic_data":"{\"mobile_number\":\"07802510578\",\"gender\":\"female\",\"dob\":\"1968-08-24\",\"nhs\":null,\"address\":\"1 The Larthings CM14FL\",\"city\":\"CHELMSFORD\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14FL\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-13 11:05:47"} 
[2025-07-22 10:21:41] local.ERROR: Failed to migrate user ID 550: Session store not set on request. {"ID":"550","user_login":"Blake_YkIXg1","user_email":"<EMAIL>","user_nicename":"blake_ykixg1","display_name":"Blake Evereff","user_registered":"2025-03-30 09:45:15","first_name":"Blake","last_name":"Evereff","phone":null,"basic_data":"{\"mobile_number\":\"07936440761\",\"gender\":\"male\",\"dob\":\"2017-01-18\",\"nhs\":null,\"address\":\"4 George Crowfoof Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm16hr\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 10:45:15"} 
[2025-07-22 10:21:42] local.ERROR: Failed to migrate user ID 462: Session store not set on request. {"ID":"462","user_login":"Bobby_SIU8RV","user_email":"<EMAIL>","user_nicename":"bobby_siu8rv","display_name":"Bobby Stanley","user_registered":"2025-03-16 14:44:18","first_name":"Bobby","last_name":"Stanley","phone":null,"basic_data":"{\"mobile_number\":\"07788908610\",\"gender\":\"male\",\"dob\":\"2019-02-22\",\"nhs\":null,\"address\":\"Paddock Farm\",\"city\":\"Hatfield Pevrel\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 2JW\",\"blood_group\":\"\",\"registered_gp_name\":\"Fern House, Witham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-16 14:44:18"} 
[2025-07-22 10:21:42] local.ERROR: Failed to migrate user ID 882: Session store not set on request. {"ID":"882","user_login":"Brandon_zgiJtK","user_email":"<EMAIL>","user_nicename":"brandon_zgijtk","display_name":"Brandon Eaglestone","user_registered":"2025-06-03 15:48:05","first_name":"Brandon","last_name":"Eaglestone","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2003-11-26\",\"nhs\":null,\"address\":\"9 Granger Row, Chelmsford CM1 4WF\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4WF\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-06 19:28:05"} 
[2025-07-22 10:21:42] local.ERROR: Failed to migrate user ID 268: Session store not set on request. {"ID":"268","user_login":"Brenda_qjgtL5","user_email":"<EMAIL>","user_nicename":"brenda_qjgtl5","display_name":"Brenda MacIntyre","user_registered":"2025-02-08 14:08:42","first_name":"Brenda","last_name":"MacIntyre","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1942-01-30\",\"nhs\":null,\"address\":\"20 the paddocks, Witham, CM8 2DR\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm8 2dr\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-08 14:14:21"} 
[2025-07-22 10:21:42] local.ERROR: Failed to migrate user ID 257: Session store not set on request. {"ID":"257","user_login":"Brenda_sdM2jS","user_email":"<EMAIL>","user_nicename":"brenda_sdm2js","display_name":"Brenda Saveall","user_registered":"2025-02-04 11:37:19","first_name":"Brenda","last_name":"Saveall","phone":null,"basic_data":"{\"mobile_number\":\"01245600527\",\"gender\":\"female\",\"dob\":\"1948-11-09\",\"nhs\":null,\"address\":\"53 Goshawk Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 8XW\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-04 11:37:19"} 
[2025-07-22 10:21:43] local.ERROR: Failed to migrate user ID 717: Session store not set on request. {"ID":"717","user_login":"Brian_F6Bdkz","user_email":"<EMAIL>","user_nicename":"brian_f6bdkz","display_name":"Brian Baxter","user_registered":"2025-04-28 10:57:14","first_name":"Brian","last_name":"Baxter","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1942-06-23\",\"nhs\":\"**********\",\"address\":\"7,Pan Walk,Chelmsford,CM1 2HD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Shahzad Ahmad\",\"registered_gp_address\":\"20,Merlin Place,Chemsford. (Tennyson House Surgery)\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-30 17:49:50"} 
[2025-07-22 10:21:43] local.ERROR: Failed to migrate user ID 650: Session store not set on request. {"ID":"650","user_login":"Carol_r89nUu","user_email":"<EMAIL>","user_nicename":"carol_r89nuu","display_name":"Carol Witchell","user_registered":"2025-04-17 17:13:55","first_name":"Carol","last_name":"Witchell","phone":null,"basic_data":"{\"mobile_number\":\"07376279206\",\"gender\":\"female\",\"dob\":\"1967-02-18\",\"nhs\":null,\"address\":\"Duffields\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM27SY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 07:02:02"} 
[2025-07-22 10:21:43] local.ERROR: Failed to migrate user ID 836: Session store not set on request. {"ID":"836","user_login":"Caroline_CK2z7S","user_email":"<EMAIL>","user_nicename":"caroline_ck2z7s","display_name":"Caroline Corkery","user_registered":"2025-05-25 11:18:01","first_name":"Caroline","last_name":"Corkery","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1991-02-13\",\"nhs\":\"\",\"address\":\"37 Skylark Walk Chelmsford CM2 8BA\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"N/A\",\"registered_gp_address\":\"Rivermead Gate Medical Centre\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-25 12:18:01"} 
[2025-07-22 10:21:43] local.ERROR: Failed to migrate user ID 973: Session store not set on request. {"ID":"973","user_login":"Caroline_HQuTMn","user_email":"<EMAIL>","user_nicename":"caroline_hqutmn","display_name":"Caroline Mould","user_registered":"2025-06-20 13:23:23","first_name":"Caroline","last_name":"Mould","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1972-08-30\",\"nhs\":\"**********\",\"address\":\"1 Highwood cottages Ingatestone rd Cm13qt\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"The New Folly Surgery\",\"registered_gp_address\":\"Bell mead, Ingatestone ,cm4 0fa\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-20 14:23:23"} 
[2025-07-22 10:21:43] local.ERROR: Failed to migrate user ID 262: Session store not set on request. {"ID":"262","user_login":"Casper_YEed5D","user_email":"<EMAIL>","user_nicename":"casper_yeed5d","display_name":"Casper Faulkner","user_registered":"2025-02-05 17:29:11","first_name":"Casper","last_name":"Faulkner","phone":null,"basic_data":"{\"mobile_number\":\"07815604999\",\"gender\":\"male\",\"dob\":\"2022-10-26\",\"nhs\":null,\"address\":\"14 Tapley Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM1 4XY\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-05 18:05:46"} 
[2025-07-22 10:21:44] local.ERROR: Failed to migrate user ID 992: Session store not set on request. {"ID":"992","user_login":"Cass_YeBfsX","user_email":"<EMAIL>","user_nicename":"cass_yebfsx","display_name":"Cass Connor","user_registered":"2025-06-25 11:30:56","first_name":"Cass","last_name":"Connor","phone":null,"basic_data":"{\"mobile_number\":\"07903234557\",\"gender\":\"female\",\"dob\":\"1980-06-20\",\"nhs\":null,\"address\":\"28 Grantham Ave, Great Notley, Braintree\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM77 7FP\",\"blood_group\":\"\",\"registered_gp_name\":\"Great Notley Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-25 12:30:56"} 
[2025-07-22 10:21:44] local.ERROR: Failed to migrate user ID 519: Session store not set on request. {"ID":"519","user_login":"Catherine_0PDCIN","user_email":"<EMAIL>","user_nicename":"catherine_0pdcin","display_name":"Catherine Wellman","user_registered":"2025-03-25 12:39:07","first_name":"Catherine","last_name":"Wellman","phone":null,"basic_data":"{\"mobile_number\":\"07702005226\",\"dob\":\"1971-03-14\",\"nhs\":\"\",\"address\":\"24 Armistice Avenue\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"NA\",\"registered_gp_address\":\"Humber road surgery, Springfield\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-25 12:39:07"} 
[2025-07-22 10:21:44] local.ERROR: Failed to migrate user ID 912: Session store not set on request. {"ID":"912","user_login":"Charles_gfnL7t","user_email":"<EMAIL>","user_nicename":"charles_gfnl7t","display_name":"Charles Bell","user_registered":"2025-06-10 13:56:03","first_name":"Charles","last_name":"Bell","phone":null,"basic_data":"{\"mobile_number\":\"07572703130\",\"dob\":\"2003-11-26\",\"nhs\":\"**********\",\"address\":\"4 Coles Green Cottages Belchamp St Paul Sudbury, Suffolk CO10 7BS\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Littler\",\"registered_gp_address\":\"Castle Hedingham Surgery, 10a Falcon Square, Castle Hedingham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-10 14:56:03"} 
[2025-07-22 10:21:44] local.ERROR: Failed to migrate user ID 439: Session store not set on request. {"ID":"439","user_login":"Charlette_cSRs5J","user_email":"<EMAIL>","user_nicename":"charlette_csrs5j","display_name":"Charlette Sullivan","user_registered":"2025-03-12 10:05:42","first_name":"Charlette","last_name":"Sullivan","phone":null,"basic_data":"{\"mobile_number\":\"07557958503\",\"gender\":\"female\",\"dob\":\"1999-02-03\",\"nhs\":null,\"address\":\"28 alderburblea Road CM34XQ\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM34XQ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-12 10:05:42"} 
[2025-07-22 10:21:45] local.ERROR: Failed to migrate user ID 610: Session store not set on request. {"ID":"610","user_login":"Charlie_0cDRr6","user_email":"<EMAIL>","user_nicename":"charlie_0cdrr6","display_name":"Charlie Blake","user_registered":"2025-04-11 15:03:59","first_name":"Charlie","last_name":"Blake","phone":null,"basic_data":"{\"mobile_number\":\"07977957813\",\"gender\":\"male\",\"dob\":\"1997-06-12\",\"nhs\":null,\"address\":\"8 coburg place South Woodham Ferrers\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"Cm35ly\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 10:41:10"} 
[2025-07-22 10:21:45] local.ERROR: Failed to migrate user ID 715: Session store not set on request. {"ID":"715","user_login":"Charlotte_rRJqY7","user_email":"<EMAIL>","user_nicename":"charlotte_rrjqy7","display_name":"Charlotte Dines","user_registered":"2025-04-27 09:52:12","first_name":"Charlotte","last_name":"Dines","phone":null,"basic_data":"{\"mobile_number\":\"07581122391\",\"gender\":\"female\",\"dob\":\"1988-01-19\",\"nhs\":null,\"address\":\"12 Woodlands Park, Leigh-on-Sea\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS9 3TY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-27 10:52:12"} 
[2025-07-22 10:21:45] local.ERROR: Failed to migrate user ID 835: Session store not set on request. {"ID":"835","user_login":"Charlotte_85sNSX","user_email":"<EMAIL>","user_nicename":"charlotte_85snsx","display_name":"Charlotte Harris","user_registered":"2025-05-25 07:04:45","first_name":"Charlotte","last_name":"Harris","phone":null,"basic_data":"{\"mobile_number\":\"07511193251\",\"dob\":\"2025-10-29\",\"nhs\":\"\",\"address\":\"Parkview Dowsett lane Billericay\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Billericay medical practice\",\"registered_gp_address\":\"Stock road, Billericay\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-25 08:04:45"} 
[2025-07-22 10:21:45] local.ERROR: Failed to migrate user ID 734: Session store not set on request. {"ID":"734","user_login":"Cheryl_iuYmbj","user_email":"<EMAIL>","user_nicename":"cheryl_iuymbj","display_name":"Cheryl Hazell","user_registered":"2025-05-02 15:10:30","first_name":"Cheryl","last_name":"Hazell","phone":null,"basic_data":"{\"mobile_number\":\"07547498822\",\"gender\":\"female\",\"dob\":\"1969-03-05\",\"nhs\":null,\"address\":\"15 Parmenter Grange Braintree 115 Highgarrett\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM75NQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Church Lane Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-02 17:02:22"} 
[2025-07-22 10:21:46] local.ERROR: Failed to migrate user ID 226: Session store not set on request. {"ID":"226","user_login":"Cheryl_Bx5dUb","user_email":"<EMAIL>","user_nicename":"cheryl_bx5dub","display_name":"Cheryl Richardson","user_registered":"2025-01-28 10:27:16","first_name":"Cheryl","last_name":"Richardson","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1957-05-18\",\"nhs\":\"Beauchamp house Surgery\",\"address\":\"67 Nicklby Road\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":\"united kingdom\",\"postal_code\":\"CM14XG\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-26 12:44:58"} 
[2025-07-22 10:21:46] local.ERROR: Failed to migrate user ID 425: Session store not set on request. {"ID":"425","user_login":"Chloe_0PqOQs","user_email":"<EMAIL>","user_nicename":"chloe_0pqoqs","display_name":"Chloe Howell","user_registered":"2025-03-09 13:58:21","first_name":"Chloe","last_name":"Howell","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1996-06-02\",\"nhs\":\"\",\"address\":\"Flat 4 Ashtree Court, 31 fobbing Road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Corringham Medical Centre\",\"registered_gp_address\":\"Flat 4 Ashtree Court, 31 fobbing Road\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-09 13:58:21"} 
[2025-07-22 10:21:46] local.ERROR: Failed to migrate user ID 900: Session store not set on request. {"ID":"900","user_login":"Chloe_Qjpvn8","user_email":"<EMAIL>","user_nicename":"chloe_qjpvn8","display_name":"Chloe Sparks","user_registered":"2025-06-08 12:57:00","first_name":"Chloe","last_name":"Sparks","phone":null,"basic_data":"{\"mobile_number\":\"07710615611\",\"gender\":\"female\",\"dob\":\"1998-03-31\",\"nhs\":null,\"address\":\"34A Condor Gate\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 3PY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-08 13:57:00"} 
[2025-07-22 10:21:46] local.ERROR: Failed to migrate user ID 711: Session store not set on request. {"ID":"711","user_login":"Chloe_IJHPdi","user_email":"<EMAIL>","user_nicename":"chloe_ijhpdi","display_name":"Chloe Tappin","user_registered":"2025-04-26 10:47:35","first_name":"Chloe","last_name":"Tappin","phone":null,"basic_data":"{\"mobile_number\":\"07912577805\",\"gender\":\"female\",\"dob\":\"2008-11-15\",\"nhs\":null,\"address\":\"1 Factory HIll\",\"city\":\"Tiptree\",\"state\":\"\",\"country\":null,\"postal_code\":\"CO5 0RE\",\"blood_group\":\"\",\"registered_gp_name\":\"Longfield Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-26 11:47:35"} 
[2025-07-22 10:21:46] local.ERROR: Failed to migrate user ID 657: Session store not set on request. {"ID":"657","user_login":"Chloe_FiSpPI","user_email":"<EMAIL>","user_nicename":"chloe_fisppi","display_name":"Chloe Thompson","user_registered":"2025-04-19 07:51:52","first_name":"Chloe","last_name":"Thompson","phone":null,"basic_data":"{\"mobile_number\":\"07850241122\",\"dob\":\"1989-07-12\",\"nhs\":\"\",\"address\":\"Field House Green Way North Barsham Walsingham NR22 6AS\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Fakenham Medical Practice\",\"registered_gp_address\":\"Trinity Road, Fakenham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-19 08:51:52"} 
[2025-07-22 10:21:47] local.ERROR: Failed to migrate user ID 580: Session store not set on request. {"ID":"580","user_login":"Christina_xcENUj","user_email":"<EMAIL>","user_nicename":"christina_xcenuj","display_name":"Christina Hall","user_registered":"2025-04-06 09:49:54","first_name":"Christina","last_name":"Hall","phone":null,"basic_data":"{\"mobile_number\":\"07715316480\",\"dob\":\"1988-04-04\",\"nhs\":\"\",\"address\":\"20 Searle Crescent, Broomfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Little waltham &amp; great notley\",\"registered_gp_address\":\"Brook hill, little waltham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 10:49:54"} 
[2025-07-22 10:21:47] local.ERROR: Failed to migrate user ID 860: Session store not set on request. {"ID":"860","user_login":"Christine_ISq9M2","user_email":"<EMAIL>","user_nicename":"christine_isq9m2","display_name":"Christine Drake","user_registered":"2025-05-31 11:00:48","first_name":"Christine","last_name":"Drake","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1946-08-15\",\"nhs\":null,\"address\":\"38 Millfields\",\"city\":\"Danbury\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 4LE\",\"blood_group\":\"\",\"registered_gp_name\":\"Danbury Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 12:00:48"} 
[2025-07-22 10:21:47] local.ERROR: Failed to migrate user ID 359: Session store not set on request. {"ID":"359","user_login":"Christine_tlD5xz","user_email":"<EMAIL>","user_nicename":"christine_tld5xz","display_name":"Christine Goldstone","user_registered":"2025-02-26 13:50:34","first_name":"Christine","last_name":"Goldstone","phone":null,"basic_data":"{\"mobile_number\":\"07982257991\",\"gender\":\"female\",\"dob\":\"1943-01-23\",\"nhs\":\"NA\",\"address\":\"24 Pickwick Ave. Chelmsford CM1 4UN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Chelmsford Medical Partnership\",\"registered_gp_address\":\"Parkside Melbourne Avenue. Chelmsford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-28 17:24:21"} 
[2025-07-22 10:21:47] local.ERROR: Failed to migrate user ID 404: Session store not set on request. {"ID":"404","user_login":"Christine_nMuXq6","user_email":"<EMAIL>","user_nicename":"christine_nmuxq6","display_name":"Christine Ilott","user_registered":"2025-03-05 11:53:45","first_name":"Christine","last_name":"Ilott","phone":null,"basic_data":"{\"mobile_number\":\"07490717844\",\"gender\":\"female\",\"dob\":\"1956-07-22\",\"nhs\":null,\"address\":\"39 ORTON CLOSE, MARGARATINE CM49JN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM49JN\",\"blood_group\":\"\",\"registered_gp_name\":\"The New Folly Ingatestone\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-05 11:53:45"} 
[2025-07-22 10:21:48] local.ERROR: Failed to migrate user ID 949: Session store not set on request. {"ID":"949","user_login":"Christopher_93Y1RQ","user_email":"<EMAIL>","user_nicename":"christopher_93y1rq","display_name":"Christopher Davies","user_registered":"2025-06-16 15:12:42","first_name":"Christopher","last_name":"Davies","phone":null,"basic_data":"{\"mobile_number\":\"07774727475\",\"gender\":\"male\",\"dob\":\"1967-09-23\",\"nhs\":null,\"address\":\"The Barn Woolmongers Lane Blackmore, Ingatestone, Essex,\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 0JX\",\"blood_group\":\"\",\"registered_gp_name\":\"Ongar Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-16 16:16:38"} 
[2025-07-22 10:21:48] local.ERROR: Failed to migrate user ID 447: Session store not set on request. {"ID":"447","user_login":"Christopher_FUe0AL","user_email":"<EMAIL>","user_nicename":"christopher_fue0al","display_name":"Christopher Field","user_registered":"2025-03-14 11:28:50","first_name":"Christopher","last_name":"Field","phone":null,"basic_data":"{\"mobile_number\":\"07960922609\",\"gender\":\"male\",\"dob\":\"1957-03-04\",\"nhs\":null,\"address\":\"10 Bramble Place , Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4GD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 16:04:01"} 
[2025-07-22 10:21:48] local.ERROR: Failed to migrate user ID 824: Session store not set on request. {"ID":"824","user_login":"Christopher_vNA6Tt","user_email":"<EMAIL>","user_nicename":"christopher_vna6tt","display_name":"Christopher Simons","user_registered":"2025-05-23 09:10:49","first_name":"Christopher","last_name":"Simons","phone":null,"basic_data":"{\"mobile_number\":\"07563430374\",\"gender\":\"male\",\"dob\":\"1954-04-21\",\"nhs\":null,\"address\":\"34 Victoria Road , Writtle\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3PA\",\"blood_group\":\"\",\"registered_gp_name\":\"Writtle House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-23 10:35:32"} 
[2025-07-22 10:21:48] local.ERROR: Failed to migrate user ID 267: Session store not set on request. {"ID":"267","user_login":"Clare_Vyob31","user_email":"<EMAIL>","user_nicename":"clare_vyob31","display_name":"Clare Causon","user_registered":"2025-02-08 10:13:34","first_name":"Clare","last_name":"Causon","phone":null,"basic_data":"{\"mobile_number\":\"07428942022\",\"dob\":\"1966-08-26\",\"nhs\":\"\",\"address\":\"26 Limetree place 8 Collingwood Rd Witham Essex\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"Cm8 2gf\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-08 10:13:34"} 
[2025-07-22 10:21:49] local.ERROR: Failed to migrate user ID 770: Session store not set on request. {"ID":"770","user_login":"Clare_2etEyZ","user_email":"<EMAIL>","user_nicename":"clare_2eteyz","display_name":"Clare Foreman","user_registered":"2025-05-11 10:19:41","first_name":"Clare","last_name":"Foreman","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1983-09-02\",\"nhs\":\"\",\"address\":\"6 Falkland close Boreham CM3 3dd\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr\",\"registered_gp_address\":\"NHS north Springfield\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-11 11:19:41"} 
[2025-07-22 10:21:49] local.ERROR: Failed to migrate user ID 327: Session store not set on request. {"ID":"327","user_login":"Claudia_OVxJYr","user_email":"<EMAIL>","user_nicename":"claudia_ovxjyr","display_name":"Claudia Dellapina","user_registered":"2025-02-17 17:41:21","first_name":"Claudia","last_name":"Dellapina","phone":null,"basic_data":"{\"mobile_number\":\"07983352337\",\"gender\":\"female\",\"dob\":\"1986-04-17\",\"nhs\":null,\"address\":\"10 Joseph Prentice Way, CM1 6BZ\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6BZ\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-17 17:45:30"} 
[2025-07-22 10:21:49] local.ERROR: Failed to migrate user ID 326: Session store not set on request. {"ID":"326","user_login":"Colin_BLvjUa","user_email":"<EMAIL>","user_nicename":"colin_blvjua","display_name":"Colin Anderson","user_registered":"2025-02-17 13:23:38","first_name":"Colin","last_name":"Anderson","phone":null,"basic_data":"{\"mobile_number\":\"07984711653\",\"gender\":\"male\",\"dob\":\"1956-04-26\",\"nhs\":null,\"address\":\"24 Weller Grove, CM1 4YJ\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 4YJ\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-17 17:17:58"} 
[2025-07-22 10:21:49] local.ERROR: Failed to migrate user ID 655: Session store not set on request. {"ID":"655","user_login":"Connor_Ghlw1c","user_email":"<EMAIL>","user_nicename":"connor_ghlw1c","display_name":"Connor Robertson","user_registered":"2025-04-18 14:27:54","first_name":"Connor","last_name":"Robertson","phone":null,"basic_data":"{\"mobile_number\":\"07506110755\",\"gender\":\"male\",\"dob\":\"1993-05-26\",\"nhs\":null,\"address\":\"89 Norman Court\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 1DR\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Gate Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":{\"label\":\"VitalityHealth\",\"insurance_provider\":\"VitalityHealth\"},\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 15:34:10"} 
[2025-07-22 10:21:49] local.ERROR: Failed to migrate user ID 1011: Session store not set on request. {"ID":"1011","user_login":"Corey_TMoQX5","user_email":"<EMAIL>","user_nicename":"corey_tmoqx5","display_name":"Corey Tecumer","user_registered":"2025-06-30 11:16:57","first_name":"Corey","last_name":"Tecumer","phone":null,"basic_data":"{\"mobile_number\":\"07898726545\",\"gender\":\"male\",\"dob\":\"2008-01-15\",\"nhs\":null,\"address\":\"23 wicklow avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2HH\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 12:16:57"} 
[2025-07-22 10:21:50] local.ERROR: Failed to migrate user ID 586: Session store not set on request. {"ID":"586","user_login":"Damian_XRvCcb","user_email":"<EMAIL>","user_nicename":"damian_xrvccb","display_name":"Damian Turburville","user_registered":"2025-04-07 09:17:07","first_name":"Damian","last_name":"Turburville","phone":null,"basic_data":"{\"mobile_number\":\"07769266913\",\"dob\":\"1978-04-25\",\"nhs\":\"**********\",\"address\":\"20 peregrine deive\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Donu2019t have one\",\"registered_gp_address\":\"Moulsham lodge\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 10:17:07"} 
[2025-07-22 10:21:50] local.ERROR: Failed to migrate user ID 862: Session store not set on request. {"ID":"862","user_login":"Daniel_ZiweW4","user_email":"<EMAIL>","user_nicename":"daniel_ziwew4","display_name":"Daniel Thornton","user_registered":"2025-05-31 14:33:01","first_name":"Daniel","last_name":"Thornton","phone":null,"basic_data":"{\"mobile_number\":\"07392202420\",\"gender\":\"male\",\"dob\":\"1993-07-02\",\"nhs\":null,\"address\":\"25 The Fryth\",\"city\":\"Basildon\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS14 3PN\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennison House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 15:33:01"} 
[2025-07-22 10:21:50] local.ERROR: Failed to migrate user ID 219: Session store not set on request. {"ID":"219","user_login":"David_Zhrik9","user_email":"<EMAIL>","user_nicename":"david_zhrik9","display_name":"David Croxon","user_registered":"2025-01-24 20:16:16","first_name":"David","last_name":"Croxon","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1967-10-08\",\"nhs\":\"\",\"address\":\"Oak House Main Road Bicknacre\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM3 4HW\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-24 20:16:16"} 
[2025-07-22 10:21:50] local.ERROR: Failed to migrate user ID 304: Session store not set on request. {"ID":"304","user_login":"David_FD0vIW","user_email":"<EMAIL>","user_nicename":"david_fd0viw","display_name":"David Gibson","user_registered":"2025-02-13 13:58:41","first_name":"David","last_name":"Gibson","phone":null,"basic_data":"{\"mobile_number\":\"07904587991\",\"gender\":\"male\",\"dob\":\"1952-07-02\",\"nhs\":null,\"address\":\"Lytchett Moulsham street Chelmsford CM29AQ\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-16 12:10:43"} 
[2025-07-22 10:21:51] local.ERROR: Failed to migrate user ID 372: Session store not set on request. {"ID":"372","user_login":"David_QqKDnA","user_email":"<EMAIL>","user_nicename":"david_qqkdna","display_name":"David Gilmour","user_registered":"2025-02-28 16:46:56","first_name":"David","last_name":"Gilmour","phone":null,"basic_data":"{\"mobile_number\":\"07875811924\",\"gender\":\"male\",\"dob\":\"1994-05-14\",\"nhs\":null,\"address\":\"36 havisham way chelmsford essex\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"David gilmour\",\"registered_gp_address\":\"36 havisham way chelmsford essex\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-02 12:41:39"} 
[2025-07-22 10:21:51] local.ERROR: Failed to migrate user ID 895: Session store not set on request. {"ID":"895","user_login":"David_Grahame_PIC8H2","user_email":"<EMAIL>","user_nicename":"david_grahame_pic8h2","display_name":"David Grahame Taplin","user_registered":"2025-06-07 12:01:05","first_name":"David Grahame","last_name":"Taplin","phone":null,"basic_data":"{\"mobile_number\":\"07860185175\",\"gender\":\"male\",\"dob\":\"1956-04-29\",\"nhs\":\"************\",\"address\":\"2, Graysons Close\",\"city\":\"Rayleigh\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS6 8LJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Dr K Monk\",\"registered_gp_address\":\"57 Eastwood Road, Rayleigh SS6 7JF\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-07 13:01:05"} 
[2025-07-22 10:21:51] local.ERROR: Failed to migrate user ID 897: Session store not set on request. {"ID":"897","user_login":"David_oYKANv","user_email":"<EMAIL>","user_nicename":"david_oykanv","display_name":"David Greenwood","user_registered":"2025-06-07 14:12:59","first_name":"David","last_name":"Greenwood","phone":null,"basic_data":"{\"mobile_number\":\"07816485136\",\"gender\":\"male\",\"dob\":\"1958-05-15\",\"nhs\":null,\"address\":\"53 St Febians Drive\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2PU\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-07 15:12:59"} 
[2025-07-22 10:21:51] local.ERROR: Failed to migrate user ID 740: Session store not set on request. {"ID":"740","user_login":"Abi_cZA7GH","user_email":"<EMAIL>","user_nicename":"abi_cza7gh","display_name":"David Howe","user_registered":"2025-05-04 10:17:35","first_name":"David","last_name":"Howe","phone":null,"basic_data":"{\"mobile_number\":\"07713103750\",\"gender\":\"female\",\"dob\":\"1972-05-22\",\"nhs\":null,\"address\":\"291 Mundon Road\",\"city\":\"Maldon\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 6PW\",\"blood_group\":\"\",\"registered_gp_name\":\"Dr Haegar\",\"registered_gp_address\":\"Blackwater Medical Centre\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-07 13:26:48"} 
[2025-07-22 10:21:52] local.ERROR: Failed to migrate user ID 724: Session store not set on request. {"ID":"724","user_login":"David_H2Z51x","user_email":"<EMAIL>","user_nicename":"david_h2z51x","display_name":"David Idialu","user_registered":"2025-04-29 11:09:44","first_name":"David","last_name":"Idialu","phone":null,"basic_data":"{\"mobile_number\":\"07960414330\",\"gender\":\"male\",\"dob\":\"1979-07-04\",\"nhs\":null,\"address\":\"2 Armstice Avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM16AR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-29 12:09:44"} 
[2025-07-22 10:21:52] local.ERROR: Failed to migrate user ID 720: Session store not set on request. {"ID":"720","user_login":"David_wIsJip","user_email":"<EMAIL>","user_nicename":"david_wisjip","display_name":"David Johnson","user_registered":"2025-04-28 14:50:07","first_name":"David","last_name":"Johnson","phone":null,"basic_data":"{\"mobile_number\":\"07813512239\",\"gender\":\"male\",\"dob\":\"1942-05-21\",\"nhs\":null,\"address\":\"27 Canudan Road chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12SU\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-28 15:50:08"} 
[2025-07-22 10:21:52] local.ERROR: Failed to migrate user ID 776: Session store not set on request. {"ID":"776","user_login":"David_kpoQgV","user_email":"<EMAIL>","user_nicename":"david_kpoqgv","display_name":"David Lush","user_registered":"2025-05-12 17:25:08","first_name":"David","last_name":"Lush","phone":null,"basic_data":"{\"mobile_number\":\"07876884162\",\"gender\":\"male\",\"dob\":\"1964-08-09\",\"nhs\":null,\"address\":\"53 Nickleby Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"C,1 4XG\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-12 18:25:08"} 
[2025-07-22 10:21:52] local.ERROR: Failed to migrate user ID 284: Session store not set on request. {"ID":"284","user_login":"David_9kVZuU","user_email":"<EMAIL>","user_nicename":"david_9kvzuu","display_name":"David Osborn","user_registered":"2025-02-10 15:58:59","first_name":"David","last_name":"Osborn","phone":null,"basic_data":"{\"mobile_number\":\"012454222287\",\"gender\":\"male\",\"dob\":\"1957-08-25\",\"nhs\":null,\"address\":\"Mill House\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 3ET\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-10 16:53:10"} 
[2025-07-22 10:21:52] local.ERROR: Failed to migrate user ID 905: Session store not set on request. {"ID":"905","user_login":"David_WxHR1v","user_email":"<EMAIL>","user_nicename":"david_wxhr1v","display_name":"David Pattrick","user_registered":"2025-06-09 09:10:03","first_name":"David","last_name":"Pattrick","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1950-02-22\",\"nhs\":\"\",\"address\":\"Westbury House Cooksmill Green\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Angela Wilson\",\"registered_gp_address\":\"Lordship Road Writtle\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-09 10:10:03"} 
[2025-07-22 10:21:53] local.ERROR: Failed to migrate user ID 460: Session store not set on request. {"ID":"460","user_login":"David_B2qnP8","user_email":"<EMAIL>","user_nicename":"david_b2qnp8","display_name":"David Wicker","user_registered":"2025-03-16 08:53:47","first_name":"David","last_name":"Wicker","phone":null,"basic_data":"{\"mobile_number\":\"07510706158\",\"dob\":\"1968-05-18\",\"nhs\":\"**********\",\"address\":\"38 Longfield Road Great Baddow Chelmsford Essex CM27QH\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Chowdury\",\"registered_gp_address\":\"Sutherland Lodge Baddow Road Chelmsford Essex\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-16 08:53:47"} 
[2025-07-22 10:21:53] local.ERROR: Failed to migrate user ID 1001: Session store not set on request. {"ID":"1001","user_login":"David_TyJXCV","user_email":"<EMAIL>","user_nicename":"david_tyjxcv","display_name":"David Young","user_registered":"2025-06-28 07:34:10","first_name":"David","last_name":"Young","phone":null,"basic_data":"{\"mobile_number\":\"07771520408\",\"dob\":\"1938-10-06\",\"nhs\":\"\",\"address\":\"8 Barrington Close Gt Baddow Cm2 7ax\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-28 08:34:11"} 
[2025-07-22 10:21:53] local.ERROR: Failed to migrate user ID 888: Session store not set on request. {"ID":"888","user_login":"Dean_97S4nt","user_email":"<EMAIL>","user_nicename":"dean_97s4nt","display_name":"Dean Gowers","user_registered":"2025-06-05 19:40:42","first_name":"Dean","last_name":"Gowers","phone":null,"basic_data":"{\"mobile_number\":\"07568382601\",\"dob\":\"1968-08-29\",\"nhs\":\"**********\",\"address\":\"27 Wickfield ash Chelmsford Essex CM14UT\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Michael Radford\",\"registered_gp_address\":\"Chelmer medical partnership 20 merlin place Chelmsford Essex CM14NW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-05 20:40:42"} 
[2025-07-22 10:21:53] local.ERROR: Failed to migrate user ID 773: Session store not set on request. {"ID":"773","user_login":"Dean_l03Nti","user_email":"<EMAIL>","user_nicename":"dean_l03nti","display_name":"Dean Grimwood","user_registered":"2025-05-12 09:22:39","first_name":"Dean","last_name":"Grimwood","phone":null,"basic_data":"{\"mobile_number\":\"07732950633\",\"gender\":\"male\",\"dob\":\"1971-03-22\",\"nhs\":null,\"address\":\"19 Springfield Braintree\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM776GE\",\"blood_group\":\"\",\"registered_gp_name\":\"Mount Chambers\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-12 10:22:39"} 
[2025-07-22 10:21:54] local.ERROR: Failed to migrate user ID 218: Session store not set on request. {"ID":"218","user_login":"Dean_S62dcE","user_email":"<EMAIL>","user_nicename":"dean_s62dce","display_name":"Dean Roberts","user_registered":"2025-01-24 16:47:21","first_name":"Dean","last_name":"Roberts","phone":null,"basic_data":"{\"mobile_number\":\"07771951435\",\"gender\":\"male\",\"dob\":\"1964-02-09\",\"nhs\":\"Tennyson house surgery\",\"address\":null,\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-26 12:05:59"} 
[2025-07-22 10:21:54] local.ERROR: Failed to migrate user ID 639: Session store not set on request. {"ID":"639","user_login":"Deborah_rAO3v6","user_email":"<EMAIL>","user_nicename":"deborah_rao3v6","display_name":"Deborah Laoye","user_registered":"2025-04-14 17:48:16","first_name":"Deborah","last_name":"Laoye","phone":null,"basic_data":"{\"mobile_number\":\"07848376367\",\"gender\":\"female\",\"dob\":\"1990-06-16\",\"nhs\":null,\"address\":\"27 Cotswold Crescent\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12HS\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-14 18:48:16"} 
[2025-07-22 10:21:54] local.ERROR: Failed to migrate user ID 1004: Session store not set on request. {"ID":"1004","user_login":"Deborah_9qZ3H1","user_email":"<EMAIL>","user_nicename":"deborah_9qz3h1","display_name":"Deborah Potton","user_registered":"2025-06-28 12:09:21","first_name":"Deborah","last_name":"Potton","phone":null,"basic_data":"{\"mobile_number\":\"07907686344\",\"gender\":\"female\",\"dob\":\"1971-06-15\",\"nhs\":null,\"address\":\"27 Hollis Lock\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 6RR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-28 13:09:21"} 
[2025-07-22 10:21:54] local.ERROR: Failed to migrate user ID 434: Session store not set on request. {"ID":"434","user_login":"Debra_YrcEaA","user_email":"<EMAIL>","user_nicename":"debra_yrceaa","display_name":"Debra Ballantine","user_registered":"2025-03-11 14:38:31","first_name":"Debra","last_name":"Ballantine","phone":null,"basic_data":"{\"mobile_number\":\"07719244466\",\"gender\":\"female\",\"dob\":\"1963-01-30\",\"nhs\":null,\"address\":\"2 Guichavon court, Guichavon Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM8 1XR\",\"blood_group\":\"\",\"registered_gp_name\":\"Witham Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-11 14:38:31"} 
[2025-07-22 10:21:54] local.ERROR: Failed to migrate user ID 614: Session store not set on request. {"ID":"614","user_login":"DEMO12_knP9zq","user_email":"<EMAIL>","user_nicename":"demo12_knp9zq","display_name":"DEMO12 PATIENT","user_registered":"2025-04-11 17:42:43","first_name":"DEMO12","last_name":"PATIENT","phone":null,"basic_data":"{\"mobile_number\":\"987654321\",\"dob\":\"2025-04-22\",\"nhs\":\"\",\"address\":\"830 Dickens Place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"830 Dickens Place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 18:42:43"} 
[2025-07-22 10:21:55] local.ERROR: Failed to migrate user ID 625: Session store not set on request. {"ID":"625","user_login":"Deon_ShDOcW","user_email":"<EMAIL>","user_nicename":"deon_shdocw","display_name":"Deon Eckhart","user_registered":"2025-04-12 13:18:11","first_name":"Deon","last_name":"Eckhart","phone":null,"basic_data":"{\"mobile_number\":\"07884114650\",\"gender\":\"male\",\"dob\":\"1967-06-30\",\"nhs\":\"**********\",\"address\":\"1 The Larthings\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM1 4FL\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham GP Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 14:34:59"} 
[2025-07-22 10:21:55] local.ERROR: Failed to migrate user ID 1008: Session store not set on request. {"ID":"1008","user_login":"Diane_IeBtEo","user_email":"<EMAIL>","user_nicename":"diane_iebteo","display_name":"Diane Burgess","user_registered":"2025-06-29 12:10:51","first_name":"Diane","last_name":"Burgess","phone":null,"basic_data":"{\"mobile_number\":\"07585972677\",\"gender\":\"female\",\"dob\":\"1981-05-12\",\"nhs\":null,\"address\":\"20 Broad street, Groom Road\",\"city\":\"Heybridge\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 8PD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-29 13:10:51"} 
[2025-07-22 10:21:55] local.ERROR: Failed to migrate user ID 800: Session store not set on request. {"ID":"800","user_login":"Diane_QveAy0","user_email":"<EMAIL>","user_nicename":"diane_qveay0","display_name":"Diane McGee","user_registered":"2025-05-18 09:02:52","first_name":"Diane","last_name":"McGee","phone":null,"basic_data":"{\"mobile_number\":\"07841182742\",\"gender\":\"female\",\"dob\":\"1974-12-14\",\"nhs\":null,\"address\":\"209 Nine Es Road\",\"city\":\"Ingatestone\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM4 0JZ\",\"blood_group\":\"\",\"registered_gp_name\":\"Ongar Hill Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 10:02:52"} 
[2025-07-22 10:21:55] local.ERROR: Failed to migrate user ID 736: Session store not set on request. {"ID":"736","user_login":"Dianne_W1zq8m","user_email":"<EMAIL>","user_nicename":"dianne_w1zq8m","display_name":"Dianne JOBSON","user_registered":"2025-05-04 07:23:24","first_name":"Dianne","last_name":"JOBSON","phone":null,"basic_data":"{\"mobile_number\":\"07973912764\",\"dob\":\"1961-05-28\",\"nhs\":\"**********\",\"address\":\"11 Weller Grove Chelmsford CM1 4YJ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Radford\",\"registered_gp_address\":\"Melbourne Park Chelmer Medical Partnership\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-04 08:23:24"} 
[2025-07-22 10:21:56] local.ERROR: Failed to migrate user ID 423: Session store not set on request. {"ID":"423","user_login":"Dolores_Uxrs3o","user_email":"<EMAIL>","user_nicename":"dolores_uxrs3o","display_name":"Dolores Matthew","user_registered":"2025-03-09 11:09:20","first_name":"Dolores","last_name":"Matthew","phone":null,"basic_data":"{\"mobile_number\":\"07983776159\",\"gender\":\"female\",\"dob\":\"2017-04-27\",\"nhs\":null,\"address\":\"35 Attwood Close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 8QJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Gate Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-09 11:09:20"} 
[2025-07-22 10:21:56] local.ERROR: Failed to migrate user ID 527: Session store not set on request. {"ID":"527","user_login":"Dominic_ytTS8X","user_email":"<EMAIL>","user_nicename":"dominic_ytts8x","display_name":"Dominic Evans","user_registered":"2025-03-26 09:08:05","first_name":"Dominic","last_name":"Evans","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1977-02-15\",\"nhs\":\"**********\",\"address\":\"63 Lady Lane Chelmsford\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Sutherland Lodge\",\"registered_gp_address\":\"115 Baddow Road, Chelmsford, CM2 7PY\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-26 09:08:05"} 
[2025-07-22 10:21:56] local.ERROR: Failed to migrate user ID 271: Session store not set on request. {"ID":"271","user_login":"Douglas_cKWyXZ","user_email":"<EMAIL>","user_nicename":"douglas_ckwyxz","display_name":"Douglas Darwin","user_registered":"2025-02-09 10:06:15","first_name":"Douglas","last_name":"Darwin","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2020-09-09\",\"nhs\":\"\",\"address\":\"42 alder drive Chelmsford Essex\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM2 9EZ\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 10:06:15"} 
[2025-07-22 10:21:56] local.ERROR: Failed to migrate user ID 431: Session store not set on request. {"ID":"431","user_login":"Douglas_BqL4D3","user_email":"<EMAIL>","user_nicename":"douglas_bql4d3","display_name":"Douglas Griffin","user_registered":"2025-03-10 10:42:09","first_name":"Douglas","last_name":"Griffin","phone":null,"basic_data":"{\"mobile_number\":\"01322951426\",\"gender\":\"male\",\"dob\":\"1951-03-16\",\"nhs\":null,\"address\":\"25 Cavendish Avenue\",\"city\":\"Erith\",\"state\":\"\",\"country\":null,\"postal_code\":\"DA8 1EB\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-15 09:53:31"} 
[2025-07-22 10:21:56] local.ERROR: Failed to migrate user ID 420: Session store not set on request. {"ID":"420","user_login":"Eduardo_aluT4J","user_email":"<EMAIL>","user_nicename":"eduardo_alut4j","display_name":"Eduardo Pacheco","user_registered":"2025-03-08 13:32:37","first_name":"Eduardo","last_name":"Pacheco","phone":null,"basic_data":"{\"mobile_number\":\"07510386711\",\"gender\":\"male\",\"dob\":\"1992-10-10\",\"nhs\":null,\"address\":\"54 The Vineyards\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 7QN\",\"blood_group\":\"\",\"registered_gp_name\":\"Village GP - Great Baddow\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-09 10:45:40"} 
[2025-07-22 10:21:57] local.ERROR: Failed to migrate user ID 935: Session store not set on request. {"ID":"935","user_login":"Elaine_U3y2xP","user_email":"<EMAIL>","user_nicename":"elaine_u3y2xp","display_name":"Elaine Cooch","user_registered":"2025-06-14 10:31:19","first_name":"Elaine","last_name":"Cooch","phone":null,"basic_data":"{\"mobile_number\":\"07506767856\",\"gender\":\"female\",\"dob\":\"1974-12-13\",\"nhs\":null,\"address\":\"98 West Avenue\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM1 2DF\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-14 11:31:19"} 
[2025-07-22 10:21:57] local.ERROR: Failed to migrate user ID 705: Session store not set on request. {"ID":"705","user_login":"Elaine_TWkoaw","user_email":"<EMAIL>","user_nicename":"elaine_twkoaw","display_name":"Elaine Gray","user_registered":"2025-04-25 12:04:05","first_name":"Elaine","last_name":"Gray","phone":null,"basic_data":"{\"mobile_number\":\"07989216348\",\"gender\":\"female\",\"dob\":\"1968-06-08\",\"nhs\":null,\"address\":\"23 Cherwell Drive\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2JJ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-25 13:04:05"} 
[2025-07-22 10:21:57] local.ERROR: Failed to migrate user ID 848: Session store not set on request. {"ID":"848","user_login":"Eleanor_r92pTd","user_email":"<EMAIL>","user_nicename":"eleanor_r92ptd","display_name":"Eleanor Breach","user_registered":"2025-05-28 11:33:56","first_name":"Eleanor","last_name":"Breach","phone":null,"basic_data":"{\"mobile_number\":\"07578334630\",\"gender\":\"female\",\"dob\":\"2007-03-13\",\"nhs\":null,\"address\":\"117 Longshots Close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 7DU\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-28 12:33:56"} 
[2025-07-22 10:21:57] local.ERROR: Failed to migrate user ID 496: Session store not set on request. {"ID":"496","user_login":"Eleanor_9Bvcsj","user_email":"<EMAIL>","user_nicename":"eleanor_9bvcsj","display_name":"Eleanor Mott-brown","user_registered":"2025-03-22 13:13:02","first_name":"Eleanor","last_name":"Mott-brown","phone":null,"basic_data":"{\"mobile_number\":\"07789078010\",\"dob\":\"1994-10-14\",\"nhs\":\"\",\"address\":\"Wallops Chelmsford road great Waltham Essex cm3 1ap\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr mackin\",\"registered_gp_address\":\"Brook street little Waltham Essex\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 13:13:02"} 
[2025-07-22 10:21:58] local.ERROR: Failed to migrate user ID 345: Session store not set on request. {"ID":"345","user_login":"Elen_pAe6oj","user_email":"<EMAIL>","user_nicename":"elen_pae6oj","display_name":"Elen Rozon","user_registered":"2025-02-24 14:18:26","first_name":"Elen","last_name":"Rozon","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1980-07-24\",\"nhs\":null,\"address\":\"06 Springs Oak , CM166SE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-24 14:18:26"} 
[2025-07-22 10:21:58] local.ERROR: Failed to migrate user ID 373: Session store not set on request. {"ID":"373","user_login":"Elijah_tDa6ms","user_email":"<EMAIL>","user_nicename":"elijah_tda6ms","display_name":"Elijah Dyer-Litchmore","user_registered":"2025-03-01 10:28:06","first_name":"Elijah","last_name":"Dyer-Litchmore","phone":null,"basic_data":"{\"mobile_number\":\"07824428705\",\"gender\":\"male\",\"dob\":\"2024-03-04\",\"nhs\":null,\"address\":\"70 Highfield Road, CM1 2NQ\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2NQ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-01 10:28:06"} 
[2025-07-22 10:21:58] local.ERROR: Failed to migrate user ID 865: Session store not set on request. {"ID":"865","user_login":"Elizabeth_81r5kv","user_email":"<EMAIL>","user_nicename":"elizabeth_81r5kv","display_name":"Elizabeth Cohen","user_registered":"2025-06-01 08:59:06","first_name":"Elizabeth","last_name":"Cohen","phone":null,"basic_data":"{\"mobile_number\":\"07870263655\",\"gender\":\"female\",\"dob\":\"1971-02-06\",\"nhs\":null,\"address\":\"60 Holloway Road\",\"city\":\"Maldon\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 4SQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Boreham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 09:59:07"} 
[2025-07-22 10:21:58] local.ERROR: Failed to migrate user ID 890: Session store not set on request. {"ID":"890","user_login":"Ella_64nmOz","user_email":"<EMAIL>","user_nicename":"ella_64nmoz","display_name":"Ella Farr","user_registered":"2025-06-06 10:38:24","first_name":"Ella","last_name":"Farr","phone":null,"basic_data":"{\"mobile_number\":\"07944711000\",\"gender\":\"female\",\"dob\":\"2011-01-21\",\"nhs\":null,\"address\":\"18 Lancaster Ave, Maldon\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"DM9 6FY\",\"blood_group\":\"\",\"registered_gp_name\":\"Longfield Medical Centre Maldon\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-06 11:38:24"} 
[2025-07-22 10:21:58] local.ERROR: Failed to migrate user ID 868: Session store not set on request. {"ID":"868","user_login":"Ella_ehdv6u","user_email":"<EMAIL>","user_nicename":"ella_ehdv6u","display_name":"Ella Fleming","user_registered":"2025-06-01 09:20:41","first_name":"Ella","last_name":"Fleming","phone":null,"basic_data":"{\"mobile_number\":\"07720650503\",\"gender\":\"female\",\"dob\":\"1999-03-08\",\"nhs\":null,\"address\":\"Flat 61, 19 Crims Court Street\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"SE1 5TE\",\"blood_group\":\"\",\"registered_gp_name\":\"Onkent Road GP\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 10:20:41"} 
[2025-07-22 10:21:59] local.ERROR: Failed to migrate user ID 490: Session store not set on request. {"ID":"490","user_login":"Ellie_IYBp3x","user_email":"<EMAIL>","user_nicename":"ellie_iybp3x","display_name":"Ellie Barkway-Winth","user_registered":"2025-03-21 10:39:00","first_name":"Ellie","last_name":"Barkway-Winth","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"2008-06-07\",\"nhs\":null,\"address\":\"105 Compton Street\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm13br\",\"blood_group\":\"\",\"registered_gp_name\":\"Witley House SURGERY\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 10:39:00"} 
[2025-07-22 10:21:59] local.ERROR: Failed to migrate user ID 768: Session store not set on request. {"ID":"768","user_login":"Ellie_KqeE6f","user_email":"<EMAIL>","user_nicename":"ellie_kqee6f","display_name":"Ellie Clark","user_registered":"2025-05-11 08:36:22","first_name":"Ellie","last_name":"Clark","phone":null,"basic_data":"{\"mobile_number\":\"07414278845\",\"dob\":\"2007-03-10\",\"nhs\":\"**********\",\"address\":\"33 Augustus Way, Witham, CM8 1HH\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Killy\",\"registered_gp_address\":\"Witham Health Centre\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-11 09:36:22"} 
[2025-07-22 10:21:59] local.ERROR: Failed to migrate user ID 861: Session store not set on request. {"ID":"861","user_login":"Ellie_MZ4Rtn","user_email":"<EMAIL>","user_nicename":"ellie_mz4rtn","display_name":"Ellie Mackenzie","user_registered":"2025-05-31 12:01:38","first_name":"Ellie","last_name":"Mackenzie","phone":null,"basic_data":"{\"mobile_number\":\"07393499807\",\"gender\":\"female\",\"dob\":\"1993-04-29\",\"nhs\":null,\"address\":\"Field View, Chelmsford Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 1RG\",\"blood_group\":\"\",\"registered_gp_name\":\"Ongar Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 13:01:38"} 
[2025-07-22 10:21:59] local.ERROR: Failed to migrate user ID 815: Session store not set on request. {"ID":"815","user_login":"Ellie_Xpdx15","user_email":"<EMAIL>","user_nicename":"ellie_xpdx15","display_name":"Ellie Spicer","user_registered":"2025-05-20 17:31:17","first_name":"Ellie","last_name":"Spicer","phone":null,"basic_data":"{\"mobile_number\":\"07908496421\",\"gender\":\"female\",\"dob\":\"2003-07-25\",\"nhs\":null,\"address\":\"18 Magnolia Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 9HU\",\"blood_group\":\"\",\"registered_gp_name\":\"New Folly Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-20 18:31:18"} 
[2025-07-22 10:22:00] local.ERROR: Failed to migrate user ID 623: Session store not set on request. {"ID":"623","user_login":"Eloise_BUXlP0","user_email":"<EMAIL>","user_nicename":"eloise_buxlp0","display_name":"Eloise Lawrence","user_registered":"2025-04-12 12:44:37","first_name":"Eloise","last_name":"Lawrence","phone":null,"basic_data":"{\"mobile_number\":\"07762436823\",\"gender\":\"female\",\"dob\":\"2003-04-23\",\"nhs\":null,\"address\":\"6 The Oaks, Benfleet, SS7 1SR\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS7 1SR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 13:44:37"} 
[2025-07-22 10:22:00] local.ERROR: Failed to migrate user ID 504: Session store not set on request. {"ID":"504","user_login":"Elsie_x1L4bo","user_email":"<EMAIL>","user_nicename":"elsie_x1l4bo","display_name":"Elsie Cooper","user_registered":"2025-03-23 12:31:47","first_name":"Elsie","last_name":"Cooper","phone":null,"basic_data":"{\"mobile_number\":\"07539285126\",\"gender\":\"female\",\"dob\":\"2024-03-11\",\"nhs\":null,\"address\":\"Mulberry Place, Wright Plane\",\"city\":\"Brentwood\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM15 0QA\",\"blood_group\":\"\",\"registered_gp_name\":\"Dilltree Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 12:31:47"} 
[2025-07-22 10:22:00] local.ERROR: Failed to migrate user ID 371: Session store not set on request. {"ID":"371","user_login":"Elsie_gShd98","user_email":"<EMAIL>","user_nicename":"elsie_gshd98","display_name":"Elsie Northfield","user_registered":"2025-02-28 16:14:58","first_name":"Elsie","last_name":"Northfield","phone":null,"basic_data":"{\"mobile_number\":\"07730554850\",\"gender\":\"female\",\"dob\":\"2023-01-01\",\"nhs\":null,\"address\":\"5 James Daniels View Chelmsford CM1 6BW\",\"city\":null,\"state\":\"\",\"country\":\"GB\",\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-03 11:37:14"} 
[2025-07-22 10:22:00] local.ERROR: Failed to migrate user ID 549: Session store not set on request. {"ID":"549","user_login":"Emma_RMjrZp","user_email":"<EMAIL>","user_nicename":"emma_rmjrzp","display_name":"Emma Baggs","user_registered":"2025-03-30 09:41:01","first_name":"Emma","last_name":"Baggs","phone":null,"basic_data":"{\"mobile_number\":\"07947804856\",\"gender\":\"female\",\"dob\":\"1982-11-29\",\"nhs\":null,\"address\":\"10 Nickleby Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14UL\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"Tennyson House Surgery\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 10:41:01"} 
[2025-07-22 10:22:00] local.ERROR: Failed to migrate user ID 553: Session store not set on request. {"ID":"553","user_login":"Emma_RY0mDh","user_email":"<EMAIL>","user_nicename":"emma_ry0mdh","display_name":"Emma Beckett-Davis","user_registered":"2025-03-30 10:24:36","first_name":"Emma","last_name":"Beckett-Davis","phone":null,"basic_data":"{\"mobile_number\":\"07960334502\",\"gender\":\"female\",\"dob\":\"1983-01-19\",\"nhs\":null,\"address\":\"104 Swiss Avenue, chlemsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12AF\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 11:24:36"} 
[2025-07-22 10:22:01] local.ERROR: Failed to migrate user ID 541: Session store not set on request. {"ID":"541","user_login":"Emma_xolpbs","user_email":"<EMAIL>","user_nicename":"emma_xolpbs","display_name":"Emma Blanks","user_registered":"2025-03-28 11:06:39","first_name":"Emma","last_name":"Blanks","phone":null,"basic_data":"{\"mobile_number\":\"07779144302\",\"gender\":\"female\",\"dob\":\"1980-04-15\",\"nhs\":null,\"address\":\"cm1 ***\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm1***\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-28 11:06:40"} 
[2025-07-22 10:22:01] local.ERROR: Failed to migrate user ID 731: Session store not set on request. {"ID":"731","user_login":"emma_f84OQb","user_email":"<EMAIL>","user_nicename":"emma_f84oqb","display_name":"Emma Kemp","user_registered":"2025-05-01 08:41:46","first_name":"Emma","last_name":"Kemp","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1986-03-17\",\"nhs\":\"************\",\"address\":\"160 Linnet Drive CHELMSFORD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Beacon health group\",\"registered_gp_address\":\"160 Linnet Drive\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-01 16:31:54"} 
[2025-07-22 10:22:01] local.ERROR: Failed to migrate user ID 957: Session store not set on request. {"ID":"957","user_login":"Emma_Ui7b8e","user_email":"<EMAIL>","user_nicename":"emma_ui7b8e","display_name":"Emma Sims","user_registered":"2025-06-18 06:16:18","first_name":"Emma","last_name":"Sims","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1981-04-03\",\"nhs\":\"**********\",\"address\":\"139 Writtle Road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Whitley house surgery\",\"registered_gp_address\":\"139 Writtle Road\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-18 07:16:18"} 
[2025-07-22 10:22:01] local.ERROR: Failed to migrate user ID 255: Session store not set on request. {"ID":"255","user_login":"Emma_L0qEb4","user_email":"<EMAIL>","user_nicename":"emma_l0qeb4","display_name":"Emma Thomas","user_registered":"2025-02-04 08:28:26","first_name":"Emma","last_name":"Thomas","phone":null,"basic_data":"{\"mobile_number\":\"07919056065\",\"gender\":\"female\",\"dob\":\"1973-08-15\",\"nhs\":\"Beauchamp house Surgery\",\"address\":\"27 Heath Drive Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 9HB\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-28 13:43:34"} 
[2025-07-22 10:22:02] local.ERROR: Failed to migrate user ID 712: Session store not set on request. {"ID":"712","user_login":"Emmanuel_5XCHAs","user_email":"<EMAIL>","user_nicename":"emmanuel_5xchas","display_name":"Emmanuel Kapeso","user_registered":"2025-04-26 12:50:36","first_name":"Emmanuel","last_name":"Kapeso","phone":null,"basic_data":"{\"mobile_number\":\"07464343498\",\"gender\":\"male\",\"dob\":\"1970-01-07\",\"nhs\":null,\"address\":\"64 Begonia Close North Springfield Chelmsford Essex CM1 6NL\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6NL\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House Surgery\",\"registered_gp_address\":\"20 Merlin Place, Chelmer Medical Partnership, Chelmsford CM1 4HW\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-26 20:58:42"} 
[2025-07-22 10:22:02] local.ERROR: Failed to migrate user ID 501: Session store not set on request. {"ID":"501","user_login":"Ernest_98sWrP","user_email":"<EMAIL>","user_nicename":"ernest_98swrp","display_name":"Ernest Andrew","user_registered":"2025-03-23 09:55:43","first_name":"Ernest","last_name":"Andrew","phone":null,"basic_data":"{\"mobile_number\":\"07774959686\",\"gender\":\"male\",\"dob\":\"2023-09-07\",\"nhs\":null,\"address\":\"58 Goldlay Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 0EJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Beecham House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 09:55:43"} 
[2025-07-22 10:22:02] local.ERROR: Failed to migrate user ID 535: Session store not set on request. {"ID":"535","user_login":"Ethan_3sHKRM","user_email":"<EMAIL>","user_nicename":"ethan_3shkrm","display_name":"Ethan Walsh","user_registered":"2025-03-27 13:32:20","first_name":"Ethan","last_name":"Walsh","phone":null,"basic_data":"{\"mobile_number\":\"07717858078\",\"dob\":\"2008-05-12\",\"nhs\":\"**********\",\"address\":\"1 The Green Chignal St James Chelmsford Essex CM1 4TY\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"other\",\"registered_gp_name\":\"Dr Savici\",\"registered_gp_address\":\"The Writtle Surgery, 16a Lordship Road, Writtle, CM1 3EH\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-27 13:32:20"} 
[2025-07-22 10:22:02] local.ERROR: Failed to migrate user ID 453: Session store not set on request. {"ID":"453","user_login":"Faith_TYidDu","user_email":"<EMAIL>","user_nicename":"faith_tyiddu","display_name":"Faith Ashenden","user_registered":"2025-03-15 07:15:56","first_name":"Faith","last_name":"Ashenden","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1991-02-12\",\"nhs\":null,\"address\":\"6 Beaulieu blvd. Chelmsford Essex cm16ea\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Not sure\",\"registered_gp_address\":\"Not sure\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-15 09:45:41"} 
[2025-07-22 10:22:02] local.ERROR: Failed to migrate user ID 675: Session store not set on request. {"ID":"675","user_login":"Fardin_eAM3tp","user_email":"<EMAIL>","user_nicename":"fardin_eam3tp","display_name":"Fardin Ahrari","user_registered":"2025-04-22 17:32:57","first_name":"Fardin","last_name":"Ahrari","phone":null,"basic_data":"{\"mobile_number\":\"07879797271\",\"gender\":\"male\",\"dob\":\"1994-10-19\",\"nhs\":\"**********\",\"address\":\"1 Priors Farm Lane Northolt UB5 5FE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"UB5 5FE\",\"blood_group\":\"\",\"registered_gp_name\":\"Islip Manor Medical Centre\",\"registered_gp_address\":\"45 Eastcote Ln, Northolt UB5 5RG\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 18:50:15"} 
[2025-07-22 10:22:03] local.ERROR: Failed to migrate user ID 555: Session store not set on request. {"ID":"555","user_login":"Faye_JW69hb","user_email":"<EMAIL>","user_nicename":"faye_jw69hb","display_name":"Faye Rides","user_registered":"2025-03-31 09:36:04","first_name":"Faye","last_name":"Rides","phone":null,"basic_data":"{\"mobile_number\":\"07944505103\",\"gender\":\"female\",\"dob\":\"1987-09-09\",\"nhs\":null,\"address\":\"171 Kings Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM\",\"blood_group\":\"\",\"registered_gp_name\":\"Tenneysonhouse Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-31 10:45:38"} 
[2025-07-22 10:22:03] local.ERROR: Failed to migrate user ID 502: Session store not set on request. {"ID":"502","user_login":"Flavio_ixC8aG","user_email":"<EMAIL>","user_nicename":"flavio_ixc8ag","display_name":"Flavio Lima","user_registered":"2025-03-23 11:00:32","first_name":"Flavio","last_name":"Lima","phone":null,"basic_data":"{\"mobile_number\":\"07515856537\",\"gender\":\"male\",\"dob\":\"1968-01-31\",\"nhs\":null,\"address\":\"Flat 5 Weldbrook House, 81 London Road\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"IM7 9DQ\",\"blood_group\":\"\",\"registered_gp_name\":\"St Edwards Medical Centre\",\"registered_gp_address\":\"Romford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 11:15:14"} 
[2025-07-22 10:22:03] local.ERROR: Failed to migrate user ID 604: Session store not set on request. {"ID":"604","user_login":"Francesca_GpogHk","user_email":"<EMAIL>","user_nicename":"francesca_gpoghk","display_name":"Francesca Capes","user_registered":"2025-04-10 11:06:33","first_name":"Francesca","last_name":"Capes","phone":null,"basic_data":"{\"mobile_number\":\"07843227919\",\"gender\":\"female\",\"dob\":\"1983-08-09\",\"nhs\":null,\"address\":\"5 cashe Terrace\",\"city\":\"Rayleigh\",\"state\":\"\",\"country\":null,\"postal_code\":\"S567HF\",\"blood_group\":\"\",\"registered_gp_name\":\"Chruchview Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-10 12:06:33"} 
[2025-07-22 10:22:03] local.ERROR: Failed to migrate user ID 421: Session store not set on request. {"ID":"421","user_login":"Francis_SxHnoX","user_email":"<EMAIL>","user_nicename":"francis_sxhnox","display_name":"Francis Sharp","user_registered":"2025-03-08 15:31:04","first_name":"Francis","last_name":"Sharp","phone":null,"basic_data":"{\"mobile_number\":\"07714105279\",\"gender\":\"female\",\"dob\":\"1944-03-28\",\"nhs\":null,\"address\":\"47 Roxwell Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2LY\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-08 15:31:04"} 
[2025-07-22 10:22:04] local.ERROR: Failed to migrate user ID 771: Session store not set on request. {"ID":"771","user_login":"Frank_eJ6koF","user_email":"<EMAIL>","user_nicename":"frank_ej6kof","display_name":"Frank Skinner","user_registered":"2025-05-11 12:27:01","first_name":"Frank","last_name":"Skinner","phone":null,"basic_data":"{\"mobile_number\":\"07960654023\",\"gender\":\"male\",\"dob\":\"1945-12-02\",\"nhs\":null,\"address\":\"18 Abingdon Court\",\"city\":\"Basildon\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS13 1RQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Baddow Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-26 12:38:37"} 
[2025-07-22 10:22:04] local.ERROR: Failed to migrate user ID 722: Session store not set on request. {"ID":"722","user_login":"Fre_gMZuwf","user_email":"<EMAIL>","user_nicename":"fre_gmzuwf","display_name":"Fred Bartholomew","user_registered":"2025-04-29 07:26:47","first_name":"Fred","last_name":"Bartholomew","phone":null,"basic_data":"{\"mobile_number\":\"07928156414\",\"gender\":\"male\",\"dob\":\"1948-11-28\",\"nhs\":null,\"address\":\"8 Vale End,Galleywood.Chelmsford Cm2 8nz\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Dr.Tier\",\"registered_gp_address\":\"Baddow Village Surgery.Longmead Avenue.Chelmsford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-01 09:57:45"} 
[2025-07-22 10:22:04] local.ERROR: Failed to migrate user ID 354: Session store not set on request. {"ID":"354","user_login":"Gabriella_F2pKLH","user_email":"<EMAIL>","user_nicename":"gabriella_f2pklh","display_name":"Gabriella Peterson","user_registered":"2025-02-25 17:11:59","first_name":"Gabriella","last_name":"Peterson","phone":null,"basic_data":"{\"mobile_number\":\"07735558787\",\"gender\":\"female\",\"dob\":\"1964-10-12\",\"nhs\":\"Little Walthem and Great Notley Surgery\",\"address\":\"30 Tufted Close CM777YE\",\"city\":null,\"state\":\"\",\"country\":\"GB\",\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-26 12:33:24"} 
[2025-07-22 10:22:04] local.ERROR: Failed to migrate user ID 927: Session store not set on request. {"ID":"927","user_login":"Garry_smD0YZ","user_email":"<EMAIL>","user_nicename":"garry_smd0yz","display_name":"Garry Downes","user_registered":"2025-06-13 15:29:46","first_name":"Garry","last_name":"Downes","phone":null,"basic_data":"{\"mobile_number\":\"07710886735\",\"gender\":\"male\",\"dob\":\"1958-06-18\",\"nhs\":null,\"address\":\"69 Micawber Way\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4UE\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-23 16:04:00"} 
[2025-07-22 10:22:04] local.ERROR: Failed to migrate user ID 827: Session store not set on request. {"ID":"827","user_login":"Gary_9T0CjQ","user_email":"<EMAIL>","user_nicename":"gary_9t0cjq","display_name":"Gary Davies","user_registered":"2025-05-23 16:56:40","first_name":"Gary","last_name":"Davies","phone":null,"basic_data":"{\"mobile_number\":\"07799242386\",\"gender\":\"male\",\"dob\":\"1961-12-28\",\"nhs\":null,\"address\":\"2a Dawsett Avenue Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 9TZ\",\"blood_group\":\"\",\"registered_gp_name\":\"Baddow Village Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-23 17:56:40"} 
[2025-07-22 10:22:05] local.ERROR: Failed to migrate user ID 855: Session store not set on request. {"ID":"855","user_login":"Gary_gMWdvP","user_email":"<EMAIL>","user_nicename":"gary_gmwdvp","display_name":"Gary Mead","user_registered":"2025-05-30 14:51:21","first_name":"Gary","last_name":"Mead","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1975-07-03\",\"nhs\":\"\",\"address\":\"12 Brian Cadman Gardens, Chitts Hill\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Wilson\",\"registered_gp_address\":\"Dr Angela Wilson, Springfield Hospital, Lawn Lane, Chelmsford, CM1 7GU\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-30 15:51:21"} 
[2025-07-22 10:22:05] local.ERROR: Failed to migrate user ID 953: Session store not set on request. {"ID":"953","user_login":"Gavin_BMsiwf","user_email":"<EMAIL>","user_nicename":"gavin_bmsiwf","display_name":"Gavin Mpaulo","user_registered":"2025-06-17 12:41:03","first_name":"Gavin","last_name":"Mpaulo","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1996-01-23\",\"nhs\":\"\",\"address\":\"flat 2, 57 the exchange, Calcutta road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Christopher Olukanni\",\"registered_gp_address\":\"Commonwealth health centre, Quebec road, rm18 7rb\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-17 13:41:03"} 
[2025-07-22 10:22:05] local.ERROR: Failed to migrate user ID 797: Session store not set on request. {"ID":"797","user_login":"Gemma_Ann_Ut7XjD","user_email":"<EMAIL>","user_nicename":"gemma_ann_ut7xjd","display_name":"Gemma Ann Hicklin","user_registered":"2025-05-17 15:02:18","first_name":"Gemma Ann","last_name":"Hicklin","phone":null,"basic_data":"{\"mobile_number\":\"07853330468\",\"gender\":\"female\",\"dob\":\"1985-11-17\",\"nhs\":null,\"address\":\"10 Queensland Crescent\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2EA\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-17 16:02:18"} 
[2025-07-22 10:22:05] local.ERROR: Failed to migrate user ID 485: Session store not set on request. {"ID":"485","user_login":"George_xW5SmY","user_email":"<EMAIL>","user_nicename":"george_xw5smy","display_name":"George Havercroft","user_registered":"2025-03-20 19:21:31","first_name":"George","last_name":"Havercroft","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2016-02-14\",\"nhs\":null,\"address\":\"6 Elder Close, Chelmsford, Essex, CM1 4FU\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Parkside Medical Centre\",\"registered_gp_address\":\"Parkside Medical Centre, Melbourne Avenue, Chelmsford, Essex CM1 2DY\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 09:52:58"} 
[2025-07-22 10:22:06] local.ERROR: Failed to migrate user ID 915: Session store not set on request. {"ID":"915","user_login":"Ghazala_UgGISy","user_email":"<EMAIL>","user_nicename":"ghazala_uggisy","display_name":"Ghazala Variyam","user_registered":"2025-06-11 11:40:06","first_name":"Ghazala","last_name":"Variyam","phone":null,"basic_data":"{\"mobile_number\":\"07465962747\",\"gender\":\"female\",\"dob\":\"1974-10-23\",\"nhs\":null,\"address\":\"2 Merry Way, Wickford. Essex\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS129SB\",\"blood_group\":\"\",\"registered_gp_name\":\"Robert Green Medical practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-11 12:40:06"} 
[2025-07-22 10:22:06] local.ERROR: Failed to migrate user ID 599: Session store not set on request. {"ID":"599","user_login":"Gillian_C8wa5y","user_email":"<EMAIL>","user_nicename":"gillian_c8wa5y","display_name":"Gillian Sherlock","user_registered":"2025-04-09 15:39:49","first_name":"Gillian","last_name":"Sherlock","phone":null,"basic_data":"{\"mobile_number\":\"07816827900\",\"gender\":\"female\",\"dob\":\"1969-08-05\",\"nhs\":null,\"address\":\"4 Haddow Mead, South Woodham Ferrers\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM37AS\",\"blood_group\":\"\",\"registered_gp_name\":\"Kingsway Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-09 20:45:42"} 
[2025-07-22 10:22:06] local.ERROR: Failed to migrate user ID 778: Session store not set on request. {"ID":"778","user_login":"Gillian_dT0PGB","user_email":"<EMAIL>","user_nicename":"gillian_dt0pgb","display_name":"Gillian Smyth","user_registered":"2025-05-13 10:07:11","first_name":"Gillian","last_name":"Smyth","phone":null,"basic_data":"{\"mobile_number\":\"07949200802\",\"gender\":\"female\",\"dob\":\"1988-04-15\",\"nhs\":null,\"address\":\"48 Greenfield , Witham\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM82FA\",\"blood_group\":\"\",\"registered_gp_name\":\"Witham Heath Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-13 11:08:04"} 
[2025-07-22 10:22:06] local.ERROR: Failed to migrate user ID 787: Session store not set on request. {"ID":"787","user_login":"Gina_VGz1l8","user_email":"<EMAIL>","user_nicename":"gina_vgz1l8","display_name":"Gina Agyepong","user_registered":"2025-05-15 17:24:08","first_name":"Gina","last_name":"Agyepong","phone":null,"basic_data":"{\"mobile_number\":\"07807018451\",\"gender\":\"female\",\"dob\":\"1972-07-10\",\"nhs\":null,\"address\":\"53 Corporation road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2AR\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-19 13:34:25"} 
[2025-07-22 10:22:07] local.ERROR: Failed to migrate user ID 663: Session store not set on request. {"ID":"663","user_login":"Giorgio_wOPG4r","user_email":"<EMAIL>","user_nicename":"giorgio_wopg4r","display_name":"Giorgio Crosher","user_registered":"2025-04-19 13:51:28","first_name":"Giorgio","last_name":"Crosher","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2023-10-05\",\"nhs\":\"\",\"address\":\"74 Roth Drive, Brentwood, CM13 2UE\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Mount Avenue Surgery\",\"registered_gp_address\":\"Mount Avenue, Shenfield, Essex\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-19 14:51:28"} 
[2025-07-22 10:22:07] local.ERROR: Failed to migrate user ID 931: Session store not set on request. {"ID":"931","user_login":"Giuliana_GVqiZY","user_email":"<EMAIL>","user_nicename":"giuliana_gvqizy","display_name":"Giuliana Fisher","user_registered":"2025-06-14 07:51:36","first_name":"Giuliana","last_name":"Fisher","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1964-02-10\",\"nhs\":\"**********\",\"address\":\"Brick house Farm, Generals Lane, Boreham, Chelmsford CM3 3GU\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"laurels surgery\",\"registered_gp_address\":\"boreham Chelmsford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-14 08:57:12"} 
[2025-07-22 10:22:07] local.ERROR: Failed to migrate user ID 646: Session store not set on request. {"ID":"646","user_login":"Gordon_c_k4EDLQ","user_email":"<EMAIL>","user_nicename":"gordon_c_k4edlq","display_name":"Gordon C Pallant","user_registered":"2025-04-17 10:06:31","first_name":"Gordon C","last_name":"Pallant","phone":null,"basic_data":"{\"mobile_number\":\"01245630033\",\"gender\":\"male\",\"dob\":\"1941-05-20\",\"nhs\":null,\"address\":\"5 Tobruk Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1\",\"blood_group\":\"\",\"registered_gp_name\":\"Micheal Radford\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-17 11:07:44"} 
[2025-07-22 10:22:07] local.ERROR: Failed to migrate user ID 554: Session store not set on request. {"ID":"554","user_login":"Gordon_TwmBCP","user_email":"<EMAIL>","user_nicename":"gordon_twmbcp","display_name":"Gordon Holdgate","user_registered":"2025-03-30 10:34:21","first_name":"Gordon","last_name":"Holdgate","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1945-10-27\",\"nhs\":\"**********\",\"address\":\"12 Highwood close Brentwood Essex Cm14 4ye\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr naeem\",\"registered_gp_address\":\"The new surgery 8 shenfield road cm15 8ab\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 11:34:21"} 
[2025-07-22 10:22:07] local.ERROR: Failed to migrate user ID 701: Session store not set on request. {"ID":"701","user_login":"Gowsi_TFAlg2","user_email":"<EMAIL>","user_nicename":"gowsi_tfalg2","display_name":"Gowsi Masi","user_registered":"2025-04-24 17:15:32","first_name":"Gowsi","last_name":"Masi","phone":null,"basic_data":"{\"mobile_number\":\"07842861295\",\"dob\":\"1994-11-13\",\"nhs\":\"\",\"address\":\"15, Donald Way, CM29JB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"North Chelmsford NHS Healthcare Centre\",\"registered_gp_address\":\"Building is next door to supermarket, access via Sainsbury\\'s Car Park, 2 White Hart Ln, Springfield, Chelmsford CM2 5EF\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-24 18:15:32"} 
[2025-07-22 10:22:08] local.ERROR: Failed to migrate user ID 358: Session store not set on request. {"ID":"358","user_login":"Grace_4NRMKo","user_email":"<EMAIL>","user_nicename":"grace_4nrmko","display_name":"Grace Buchanan-Kilbey","user_registered":"2025-02-26 13:44:24","first_name":"Grace","last_name":"Buchanan-Kilbey","phone":null,"basic_data":"{\"mobile_number\":\"07944757054\",\"gender\":\"female\",\"dob\":\"1990-12-12\",\"nhs\":\"NA\",\"address\":\"10 Rose Lawnfield Rd\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM1 7GB\",\"blood_group\":\"\",\"registered_gp_name\":\"NA\",\"registered_gp_address\":\"NA\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-26 13:44:24"} 
[2025-07-22 10:22:08] local.ERROR: Failed to migrate user ID 252: Session store not set on request. {"ID":"252","user_login":"Grace_Gvd40n","user_email":"<EMAIL>","user_nicename":"grace_gvd40n","display_name":"Grace Smith","user_registered":"2025-02-03 19:49:17","first_name":"Grace","last_name":"Smith","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2016-09-03\",\"nhs\":\"\",\"address\":\"Keston, Moulsham Street\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"Cm2 0jj\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-03 19:49:17"} 
[2025-07-22 10:22:08] local.ERROR: Failed to migrate user ID 761: Session store not set on request. {"ID":"761","user_login":"Grant_1hlI5m","user_email":"<EMAIL>","user_nicename":"grant_1hli5m","display_name":"Grant Easton","user_registered":"2025-05-09 19:51:42","first_name":"Grant","last_name":"Easton","phone":null,"basic_data":"{\"mobile_number\":\"07460397577\",\"dob\":\"1974-06-16\",\"nhs\":\"\",\"address\":\"2 Maple drive Chigwell Essex IG7 4GQ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Larh\",\"registered_gp_address\":\"Loughton Surgery, 25 Traps Hill, Loughton, IG10 1SZ\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-09 20:51:42"} 
[2025-07-22 10:22:08] local.ERROR: Failed to migrate user ID 578: Session store not set on request. {"ID":"578","user_login":"Hannah_2fCAoI","user_email":"<EMAIL>","user_nicename":"hannah_2fcaoi","display_name":"Hannah Belcher Bennett","user_registered":"2025-04-06 09:08:28","first_name":"Hannah","last_name":"Belcher Bennett","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1989-03-04\",\"nhs\":\"************\",\"address\":\"90 Main Road Broomfield CM1 7AA\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Not sure\",\"registered_gp_address\":\"Chelmer Medical Partnership\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 10:08:28"} 
[2025-07-22 10:22:09] local.ERROR: Failed to migrate user ID 757: Session store not set on request. {"ID":"757","user_login":"Harry_IS9zea","user_email":"<EMAIL>","user_nicename":"harry_is9zea","display_name":"Harry Bruns","user_registered":"2025-05-08 19:17:37","first_name":"Harry","last_name":"Bruns","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2003-12-20\",\"nhs\":\"\",\"address\":\"13 derwent court, Hobart close\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Melbourne house\",\"registered_gp_address\":\"13 derwent court,\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-08 20:17:37"} 
[2025-07-22 10:22:09] local.ERROR: Failed to migrate user ID 489: Session store not set on request. {"ID":"489","user_login":"Harry_bBgAko","user_email":"<EMAIL>","user_nicename":"harry_bbgako","display_name":"Harry Eckhart","user_registered":"2025-03-21 10:12:55","first_name":"Harry","last_name":"Eckhart","phone":null,"basic_data":"{\"mobile_number\":\"07802510578\",\"gender\":\"male\",\"dob\":\"2012-01-05\",\"nhs\":null,\"address\":\"1 The Larthings, Chelmsford , CM1 4FL\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4FL\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 10:12:55"} 
[2025-07-22 10:22:09] local.ERROR: Failed to migrate user ID 461: Session store not set on request. {"ID":"461","user_login":"Hassan_TCSB7P","user_email":"<EMAIL>","user_nicename":"hassan_tcsb7p","display_name":"Hassan Ali","user_registered":"2025-03-16 10:29:28","first_name":"Hassan","last_name":"Ali","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"2022-02-28\",\"nhs\":null,\"address\":\"6 Blundell Road\",\"city\":\"Luton\",\"state\":\"\",\"country\":null,\"postal_code\":\"LU3 1SH\",\"blood_group\":\"\",\"registered_gp_name\":\"Swanscombe Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-16 10:29:28"} 
[2025-07-22 10:22:09] local.ERROR: Failed to migrate user ID 924: Session store not set on request. {"ID":"924","user_login":"Heather_brGwnQ","user_email":"<EMAIL>","user_nicename":"heather_brgwnq","display_name":"Heather Vine","user_registered":"2025-06-12 17:55:58","first_name":"Heather","last_name":"Vine","phone":null,"basic_data":"{\"mobile_number\":\"07394532673\",\"gender\":\"female\",\"dob\":\"1971-07-16\",\"nhs\":null,\"address\":\"29 Thames Ave, Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2LN\",\"blood_group\":\"\",\"registered_gp_name\":\"Writtle Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-12 18:55:58"} 
[2025-07-22 10:22:09] local.ERROR: Failed to migrate user ID 839: Session store not set on request. {"ID":"839","user_login":"Helen_qNJ0du","user_email":"<EMAIL>","user_nicename":"helen_qnj0du","display_name":"Helen Saywell","user_registered":"2025-05-25 17:43:36","first_name":"Helen","last_name":"Saywell","phone":null,"basic_data":"{\"mobile_number\":\"07786511352\",\"dob\":\"1957-09-27\",\"nhs\":\"\",\"address\":\"The Bells Mashbury Road Chignal Smealey CM1 4TD\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"Little Waltham Surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-25 18:43:36"} 
[2025-07-22 10:22:10] local.ERROR: Failed to migrate user ID 853: Session store not set on request. {"ID":"853","user_login":"Henry_OIbYCH","user_email":"<EMAIL>","user_nicename":"henry_oibych","display_name":"Henry Newman","user_registered":"2025-05-29 16:06:31","first_name":"Henry","last_name":"Newman","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"2021-03-28\",\"nhs\":null,\"address\":\"Brick Kiln cottage , boyton cross lane\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4LD\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Gate Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-29 17:31:26"} 
[2025-07-22 10:22:10] local.ERROR: Failed to migrate user ID 738: Session store not set on request. {"ID":"738","user_login":"Hilary_oGnW8p","user_email":"<EMAIL>","user_nicename":"hilary_ognw8p","display_name":"Hilary Hill","user_registered":"2025-05-04 09:52:08","first_name":"Hilary","last_name":"Hill","phone":null,"basic_data":"{\"mobile_number\":\"07534890577\",\"gender\":\"female\",\"dob\":\"1960-03-19\",\"nhs\":null,\"address\":\"Barn Cottage, Old Barn Lane, Rettingdon Common\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 8HA\",\"blood_group\":\"\",\"registered_gp_name\":\"Longfield Medical Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-04 10:52:08"} 
[2025-07-22 10:22:10] local.ERROR: Failed to migrate user ID 230: Session store not set on request. {"ID":"230","user_login":"Honora_ICbQGH","user_email":"<EMAIL>","user_nicename":"honora_icbqgh","display_name":"Honora Essex","user_registered":"2025-01-29 13:17:17","first_name":"Honora","last_name":"Essex","phone":null,"basic_data":"{\"mobile_number\":\"07966530859\",\"gender\":\"female\",\"dob\":\"1932-03-03\",\"nhs\":\"**********\",\"address\":\"Rossdene, Rossdene Gardens Leaden Roding Essex CM6 1TR\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 1TR\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-29 13:27:14"} 
[2025-07-22 10:22:10] local.ERROR: Failed to migrate user ID 666: Session store not set on request. {"ID":"666","user_login":"Hugh_YiJnLf","user_email":"<EMAIL>","user_nicename":"hugh_yijnlf","display_name":"Hugh Arthur","user_registered":"2025-04-21 07:46:10","first_name":"Hugh","last_name":"Arthur","phone":null,"basic_data":"{\"mobile_number\":\"07977743354\",\"dob\":\"1955-11-06\",\"nhs\":\"**********\",\"address\":\"16 Coombe Rise Shenfield Essex CM15 8JJ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Neill\",\"registered_gp_address\":\"Mount Avenue Surgery Shenfield\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-21 08:46:10"} 
[2025-07-22 10:22:11] local.ERROR: Failed to migrate user ID 600: Session store not set on request. {"ID":"600","user_login":"Hugo_3lOK0y","user_email":"<EMAIL>","user_nicename":"hugo_3lok0y","display_name":"Hugo Amne","user_registered":"2025-04-09 16:35:47","first_name":"Hugo","last_name":"Amne","phone":null,"basic_data":"{\"mobile_number\":\"07852321715\",\"gender\":\"male\",\"dob\":\"2022-01-15\",\"nhs\":null,\"address\":\"9 Staines Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM16FR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-09 17:35:47"} 
[2025-07-22 10:22:11] local.ERROR: Failed to migrate user ID 867: Session store not set on request. {"ID":"867","user_login":"Hussain_9C8tYp","user_email":"<EMAIL>","user_nicename":"hussain_9c8typ","display_name":"Hussain Ali","user_registered":"2025-06-01 09:02:59","first_name":"Hussain","last_name":"Ali","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"2022-02-28\",\"nhs\":null,\"address\":\"12A Gerard Road\",\"city\":\"London\",\"state\":\"\",\"country\":null,\"postal_code\":\"HA1 2ND\",\"blood_group\":\"\",\"registered_gp_name\":\"Swanscombe Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 10:02:59"} 
[2025-07-22 10:22:11] local.ERROR: Failed to migrate user ID 788: Session store not set on request. {"ID":"788","user_login":"Ian_wapfdz","user_email":"<EMAIL>","user_nicename":"ian_wapfdz","display_name":"Ian Livingstone","user_registered":"2025-05-15 17:29:03","first_name":"Ian","last_name":"Livingstone","phone":null,"basic_data":"{\"mobile_number\":\"07983339419\",\"gender\":\"male\",\"dob\":\"1982-10-08\",\"nhs\":null,\"address\":\"31 Longford way\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 2LT\",\"blood_group\":\"\",\"registered_gp_name\":\"Mount Avenue Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-15 18:29:03"} 
[2025-07-22 10:22:11] local.ERROR: Failed to migrate user ID 653: Session store not set on request. {"ID":"653","user_login":"Inderjeet_FYfgaz","user_email":"<EMAIL>","user_nicename":"inderjeet_fyfgaz","display_name":"Inderjeet Singh","user_registered":"2025-04-18 09:22:05","first_name":"Inderjeet","last_name":"Singh","phone":null,"basic_data":"{\"mobile_number\":\"07543465953\",\"gender\":\"male\",\"dob\":\"1999-02-02\",\"nhs\":null,\"address\":\"26 Kenilworth Gardens\",\"city\":\"Hayes\",\"state\":\"\",\"country\":null,\"postal_code\":\"UB4 0AY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 10:22:05"} 
[2025-07-22 10:22:12] local.ERROR: Failed to migrate user ID 450: Session store not set on request. {"ID":"450","user_login":"Iqbal_rCu1iD","user_email":"<EMAIL>","user_nicename":"iqbal_rcu1id","display_name":"Iqbal Hussain","user_registered":"2025-03-14 14:58:54","first_name":"Iqbal","last_name":"Hussain","phone":null,"basic_data":"{\"mobile_number\":\"07852123577\",\"gender\":\"male\",\"dob\":\"1970-11-16\",\"nhs\":null,\"address\":\"231 Rainham Road\",\"city\":\"Rainham\",\"state\":\"\",\"country\":\"Essex\",\"postal_code\":\"RM13 7SD\",\"blood_group\":\"\",\"registered_gp_name\":\"Harlow Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-14 18:01:46"} 
[2025-07-22 10:22:12] local.ERROR: Failed to migrate user ID 516: Session store not set on request. {"ID":"516","user_login":"Isabel_XO5kBN","user_email":"<EMAIL>","user_nicename":"isabel_xo5kbn","display_name":"Isabel Da Silva","user_registered":"2025-03-25 09:40:18","first_name":"Isabel","last_name":"Da Silva","phone":null,"basic_data":"{\"mobile_number\":\"+447857599461\",\"dob\":\"1984-01-25\",\"nhs\":\"\",\"address\":\"5 Gardiner way Chelmsford\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Booth\",\"registered_gp_address\":\"Melbourne house surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-25 09:40:18"} 
[2025-07-22 10:22:12] local.ERROR: Failed to migrate user ID 240: Session store not set on request. {"ID":"240","user_login":"Isabelle_Wb7cBG","user_email":"<EMAIL>","user_nicename":"isabelle_wb7cbg","display_name":"Isabelle Morley-Short","user_registered":"2025-02-01 13:08:32","first_name":"Isabelle","last_name":"Morley-Short","phone":null,"basic_data":"{\"mobile_number\":\"07766398431\",\"gender\":\"female\",\"dob\":\"2019-12-06\",\"nhs\":null,\"address\":\"2, Blacksmiths Cottage, Main Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM3 3AA\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-01 13:08:32"} 
[2025-07-22 10:22:12] local.ERROR: Failed to migrate user ID 647: Session store not set on request. {"ID":"647","user_login":"Ishaq_LAaBQo","user_email":"<EMAIL>","user_nicename":"ishaq_laabqo","display_name":"Ishaq Ahmed","user_registered":"2025-04-17 14:52:00","first_name":"Ishaq","last_name":"Ahmed","phone":null,"basic_data":"{\"mobile_number\":\"07944056546\",\"gender\":\"male\",\"dob\":\"2015-02-13\",\"nhs\":null,\"address\":\"44 Westbourne Grove, Great Baddow, Chelmsford, CM2 9RU\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM19RU\",\"blood_group\":\"\",\"registered_gp_name\":\"Sutherland Lodge Surgery\",\"registered_gp_address\":\"115 Baddow Rd, Great Baddow, Chelmsford CM2 7PY\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-17 18:51:46"} 
[2025-07-22 10:37:11] local.ERROR: Failed to migrate user ID 825: Session store not set on request. {"ID":"825","user_login":"Ivy_jYLcCa","user_email":"<EMAIL>","user_nicename":"ivy_jylcca","display_name":"Ivy Baker","user_registered":"2025-05-23 14:04:22","first_name":"Ivy","last_name":"Baker","phone":null,"basic_data":"{\"mobile_number\":\"07810377178\",\"gender\":\"female\",\"dob\":\"1936-02-11\",\"nhs\":null,\"address\":\"39 Cilbourne Road, SS7 4AN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS7 4AN\",\"blood_group\":\"\",\"registered_gp_name\":\"Essex Way Benfleet\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-23 16:00:29"} 
[2025-07-22 10:37:11] local.ERROR: Failed to migrate user ID 632: Session store not set on request. {"ID":"632","user_login":"Jack_TgP8Xa","user_email":"<EMAIL>","user_nicename":"jack_tgp8xa","display_name":"Jack Steadman","user_registered":"2025-04-13 14:40:32","first_name":"Jack","last_name":"Steadman","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2004-09-12\",\"nhs\":\"**********\",\"address\":\"118C, Moulsham Street\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Claypath medical center\",\"registered_gp_address\":\"26 Gilesgate, Durham, DH1 1QW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 15:40:32"} 
[2025-07-22 10:37:11] local.ERROR: Failed to migrate user ID 508: Session store not set on request. {"ID":"508","user_login":"Jacqueline_Ann_b9P5J8","user_email":"<EMAIL>","user_nicename":"jacqueline_ann_b9p5j8","display_name":"Jacqueline Ann Austin","user_registered":"2025-03-24 10:32:17","first_name":"Jacqueline Ann","last_name":"Austin","phone":null,"basic_data":"{\"mobile_number\":\"07749016598\",\"gender\":\"female\",\"dob\":\"1966-02-05\",\"nhs\":null,\"address\":\"113 sT nAZAIRE , cm1 2eg\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12EG\",\"blood_group\":\"\",\"registered_gp_name\":\"DR booth MELBOURNE hOUSE\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-24 10:32:17"} 
[2025-07-22 10:37:12] local.ERROR: Failed to migrate user ID 617: Session store not set on request. {"ID":"617","user_login":"Jacqueline_54UgQp","user_email":"<EMAIL>","user_nicename":"jacqueline_54ugqp","display_name":"Jacqueline Bolton","user_registered":"2025-04-11 22:41:08","first_name":"Jacqueline","last_name":"Bolton","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1964-01-24\",\"nhs\":\"\",\"address\":\"17 Little Fields Danbury\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Dollery\",\"registered_gp_address\":\"17 Little Fields\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 23:41:08"} 
[2025-07-22 10:37:12] local.ERROR: Failed to migrate user ID 1009: Session store not set on request. {"ID":"1009","user_login":"Jacqueline_ZFeNS6","user_email":"<EMAIL>","user_nicename":"jacqueline_zfens6","display_name":"Jacqueline James","user_registered":"2025-06-29 12:14:57","first_name":"Jacqueline","last_name":"James","phone":null,"basic_data":"{\"mobile_number\":\"07966373152\",\"gender\":\"female\",\"dob\":\"1967-04-05\",\"nhs\":null,\"address\":\"North View\",\"city\":\"Witham\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 6SP\",\"blood_group\":\"\",\"registered_gp_name\":\"Danbury Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-29 13:14:57"} 
[2025-07-22 10:37:12] local.ERROR: Failed to migrate user ID 498: Session store not set on request. {"ID":"498","user_login":"Jacqueline_kFKJ3i","user_email":"<EMAIL>","user_nicename":"jacqueline_kfkj3i","display_name":"Jacqueline Payne","user_registered":"2025-03-22 16:19:53","first_name":"Jacqueline","last_name":"Payne","phone":null,"basic_data":"{\"mobile_number\":\"07521999750\",\"gender\":\"female\",\"dob\":\"1957-12-20\",\"nhs\":null,\"address\":\"6 Bedford Close\",\"city\":\"Rayleigh\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS6 7QR\",\"blood_group\":\"\",\"registered_gp_name\":\"Audley Mills\",\"registered_gp_address\":\"Rayleigh\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 16:19:53"} 
[2025-07-22 10:37:12] local.ERROR: Failed to migrate user ID 817: Session store not set on request. {"ID":"817","user_login":"Jacqueline_3ZprlF","user_email":"<EMAIL>","user_nicename":"jacqueline_3zprlf","display_name":"Jacqueline Turner","user_registered":"2025-05-21 10:23:03","first_name":"Jacqueline","last_name":"Turner","phone":null,"basic_data":"{\"mobile_number\":\"07880512228\",\"dob\":\"1953-02-24\",\"nhs\":\"\",\"address\":\"31 Ridley Road Chelmsford CM1 7AR\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Tennyson house surgery\",\"registered_gp_address\":\"20 Merlin place, Chelmsford, CM1 4HW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-21 11:23:03"} 
[2025-07-22 10:37:12] local.ERROR: Failed to migrate user ID 818: Session store not set on request. {"ID":"818","user_login":"Jacqueline_xzJbY9","user_email":"<EMAIL>","user_nicename":"jacqueline_xzjby9","display_name":"Jacqueline Turner","user_registered":"2025-05-21 10:27:16","first_name":"Jacqueline","last_name":"Turner","phone":null,"basic_data":"{\"mobile_number\":\"07880512228\",\"dob\":\"1953-02-24\",\"nhs\":\"\",\"address\":\"31 Ridley Road Chelmsford CM1 7AR\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Tennyson house surgery\",\"registered_gp_address\":\"20 Merlin place, Chelmsford, CM1 4HW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-21 11:27:16"} 
[2025-07-22 10:37:13] local.ERROR: Failed to migrate user ID 707: Session store not set on request. {"ID":"707","user_login":"Jade_RuNioZ","user_email":"<EMAIL>","user_nicename":"jade_runioz","display_name":"Jade cox","user_registered":"2025-04-25 13:29:15","first_name":"Jade","last_name":"cox","phone":null,"basic_data":"{\"mobile_number\":\"07807178493\",\"dob\":\"1993-03-17\",\"nhs\":\"\",\"address\":\"59 St Johnu2019s road Stansted Essex cm24 8js\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"dr whooley\",\"registered_gp_address\":\"peacock surgery elsenham station road\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-25 14:29:16"} 
[2025-07-22 10:37:13] local.ERROR: Failed to migrate user ID 641: Session store not set on request. {"ID":"641","user_login":"Jaidha_veJn5t","user_email":"<EMAIL>","user_nicename":"jaidha_vejn5t","display_name":"Jaidha Begum","user_registered":"2025-04-15 14:19:55","first_name":"Jaidha","last_name":"Begum","phone":null,"basic_data":"{\"mobile_number\":\"07359104338\",\"gender\":\"female\",\"dob\":\"1991-11-20\",\"nhs\":null,\"address\":\"2\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS142RD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-15 15:24:02"} 
[2025-07-22 10:37:13] local.ERROR: Failed to migrate user ID 543: Session store not set on request. {"ID":"543","user_login":"Jake_iXaHu6","user_email":"<EMAIL>","user_nicename":"jake_ixahu6","display_name":"Jake Locke","user_registered":"2025-03-28 19:54:00","first_name":"Jake","last_name":"Locke","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1989-03-28\",\"nhs\":\"************\",\"address\":\"16 tutors way, south Woodham Ferrers, Chelmsford, Essex, cm35fb\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Kishore Krishnamurthy\",\"registered_gp_address\":\"Kingsway Surgery, Crouch Vale Medical Centre, Burnham Rd, South Woodham Ferrers, Chelmsford CM3 5QP\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-28 19:54:00"} 
[2025-07-22 10:37:13] local.ERROR: Failed to migrate user ID 801: Session store not set on request. {"ID":"801","user_login":"James_lBK5om","user_email":"<EMAIL>","user_nicename":"james_lbk5om","display_name":"James Botham","user_registered":"2025-05-18 09:05:32","first_name":"James","last_name":"Botham","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1979-07-04\",\"nhs\":\"\",\"address\":\"Woodside, Chignal Smealy, Chelmsford CM1 4SU\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Unsure of doctors name\",\"registered_gp_address\":\"Writtle surgery, lordship road, Writtle\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 10:05:32"} 
[2025-07-22 10:37:14] local.ERROR: Failed to migrate user ID 402: Session store not set on request. {"ID":"402","user_login":"Jamie_2OtpJD","user_email":"<EMAIL>","user_nicename":"jamie_2otpjd","display_name":"Jamie Kikan","user_registered":"2025-03-04 20:41:14","first_name":"Jamie","last_name":"Kikan","phone":null,"basic_data":"{\"mobile_number\":\"07506267237\",\"gender\":\"female\",\"dob\":\"1989-07-19\",\"nhs\":\"************\",\"address\":\"55 tortoise shell way Braintree Essex Cm7 1wg\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Dr Gill\",\"registered_gp_address\":\"Blandford medical centre Mace Avenue, Braintree, Essex, CM7 2AE\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 09:53:54"} 
[2025-07-22 10:37:14] local.ERROR: Failed to migrate user ID 1005: Session store not set on request. {"ID":"1005","user_login":"Jane_j34iKJ","user_email":"<EMAIL>","user_nicename":"jane_j34ikj","display_name":"Jane Hunt","user_registered":"2025-06-28 14:23:21","first_name":"Jane","last_name":"Hunt","phone":null,"basic_data":"{\"mobile_number\":\"07961805380\",\"gender\":\"female\",\"dob\":\"1968-06-26\",\"nhs\":null,\"address\":\"Judds Cottage\",\"city\":\"Keers Green\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 1PQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Angel Lane\",\"registered_gp_address\":\"Dunmow\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-28 15:23:21"} 
[2025-07-22 10:37:14] local.ERROR: Failed to migrate user ID 577: Session store not set on request. {"ID":"577","user_login":"Janet_OBQ3Cq","user_email":"<EMAIL>","user_nicename":"janet_obq3cq","display_name":"Janet Brennan","user_registered":"2025-04-06 08:15:23","first_name":"Janet","last_name":"Brennan","phone":null,"basic_data":"{\"mobile_number\":\"07989312576\",\"gender\":\"female\",\"dob\":\"1932-02-20\",\"nhs\":\"**********\",\"address\":\"51 Callow Court Seymour Street Chelmsford CM1 2XN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 11:39:32"} 
[2025-07-22 10:37:15] local.ERROR: Failed to migrate user ID 791: Session store not set on request. {"ID":"791","user_login":"Janet_217N0l","user_email":"<EMAIL>","user_nicename":"janet_217n0l","display_name":"Janet How","user_registered":"2025-05-16 18:29:07","first_name":"Janet","last_name":"How","phone":null,"basic_data":"{\"mobile_number\":\"07930944961\",\"dob\":\"1962-05-10\",\"nhs\":\"**********\",\"address\":\"14 Bassett Gardens North Weald Essex. CM16 6DB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Susan Hanger\",\"registered_gp_address\":\"The Limes Medical Centre, The Plain, Epping\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-16 19:29:07"} 
[2025-07-22 10:37:15] local.ERROR: Failed to migrate user ID 330: Session store not set on request. {"ID":"330","user_login":"Janet_5WB0dX","user_email":"<EMAIL>","user_nicename":"janet_5wb0dx","display_name":"Janet Jarvis","user_registered":"2025-02-20 11:01:30","first_name":"Janet","last_name":"Jarvis","phone":null,"basic_data":"{\"mobile_number\":\"01245443465\",\"gender\":\"female\",\"dob\":\"1942-02-26\",\"nhs\":null,\"address\":\"21 Madeline Place CM14XD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-20 11:01:30"} 
[2025-07-22 10:37:15] local.ERROR: Failed to migrate user ID 531: Session store not set on request. {"ID":"531","user_login":"Janet_cdbUAy","user_email":"<EMAIL>","user_nicename":"janet_cdbuay","display_name":"Janet Stocks","user_registered":"2025-03-26 13:29:11","first_name":"Janet","last_name":"Stocks","phone":null,"basic_data":"{\"mobile_number\":\"07771553972\",\"gender\":\"female\",\"dob\":\"1953-07-22\",\"nhs\":null,\"address\":\"42 SUNNINGDALE ROAD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12NH\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer medical\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-26 13:29:11"} 
[2025-07-22 10:37:15] local.ERROR: Failed to migrate user ID 388: Session store not set on request. {"ID":"388","user_login":"Janette_F83tlH","user_email":"<EMAIL>","user_nicename":"janette_f83tlh","display_name":"Janette Potter","user_registered":"2025-03-03 17:12:31","first_name":"Janette","last_name":"Potter","phone":null,"basic_data":"{\"mobile_number\":\"07712586144\",\"gender\":\"female\",\"dob\":\"1949-06-04\",\"nhs\":\"**********\",\"address\":\"Three Chimneys, Galleywood Common, Chelmsford CM28JX Beauchamp house Surgery\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM28JX\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-03 17:54:27"} 
[2025-07-22 10:37:16] local.ERROR: Failed to migrate user ID 859: Session store not set on request. {"ID":"859","user_login":"Janis_emEBPc","user_email":"<EMAIL>","user_nicename":"janis_emebpc","display_name":"Janis Smith","user_registered":"2025-05-31 10:23:12","first_name":"Janis","last_name":"Smith","phone":null,"basic_data":"{\"mobile_number\":\"07932446026\",\"gender\":\"female\",\"dob\":\"1949-02-24\",\"nhs\":null,\"address\":\"15 Oasis Court\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 6JU\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 11:23:12"} 
[2025-07-22 10:37:16] local.ERROR: Failed to migrate user ID 261: Session store not set on request. {"ID":"261","user_login":"Holly_KBJavc","user_email":"<EMAIL>","user_nicename":"holly_kbjavc","display_name":"Jasmine Hella Christina Newell","user_registered":"2025-02-05 12:06:11","first_name":"Jasmine Hella Christina","last_name":"Newell","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"2013-08-19\",\"nhs\":null,\"address\":\"53 Cheviot Drive, Chelmsford, Essex, CM1 2EX\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2EX\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-07 16:53:58"} 
[2025-07-22 10:37:16] local.ERROR: Failed to migrate user ID 298: Session store not set on request. {"ID":"298","user_login":"Jasmir_PCxaMq","user_email":"<EMAIL>","user_nicename":"jasmir_pcxamq","display_name":"Jasmir Abdul","user_registered":"2025-02-12 16:54:53","first_name":"Jasmir","last_name":"Abdul","phone":null,"basic_data":"{\"mobile_number\":\"07463749492\",\"gender\":\"male\",\"dob\":\"2004-09-06\",\"nhs\":null,\"address\":\"17 old moor\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":\"united kingdom\",\"postal_code\":\"CM31GX\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-12 16:54:53"} 
[2025-07-22 10:37:16] local.ERROR: Failed to migrate user ID 838: Session store not set on request. {"ID":"838","user_login":"Jason_vGFYI0","user_email":"<EMAIL>","user_nicename":"jason_vgfyi0","display_name":"Jason Pelosi","user_registered":"2025-05-25 13:13:39","first_name":"Jason","last_name":"Pelosi","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1984-01-23\",\"nhs\":\"\",\"address\":\"4 the Manor House of Sutton, 175 london road, Stapleford Tawney, Essex, rm4 1bf\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Susan rogers-weedon\",\"registered_gp_address\":\"The Holly Hospital\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-25 14:13:39"} 
[2025-07-22 10:37:17] local.ERROR: Failed to migrate user ID 794: Session store not set on request. {"ID":"794","user_login":"Jayne_y2J4H1","user_email":"<EMAIL>","user_nicename":"jayne_y2j4h1","display_name":"Jayne Bridgeman","user_registered":"2025-05-17 08:49:22","first_name":"Jayne","last_name":"Bridgeman","phone":null,"basic_data":"{\"mobile_number\":\"07943952329\",\"dob\":\"1976-11-13\",\"nhs\":\"**********\",\"address\":\"103 Mariners Way, Maldon, CM96YX\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Ridley\",\"registered_gp_address\":\"Blackwater medical centre, Princes Road, Maldon, CM95GP\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-17 09:49:22"} 
[2025-07-22 10:37:17] local.ERROR: Failed to migrate user ID 321: Session store not set on request. {"ID":"321","user_login":"Jayne_fZI3uP","user_email":"<EMAIL>","user_nicename":"jayne_fzi3up","display_name":"Jayne Caleno","user_registered":"2025-02-16 10:58:08","first_name":"Jayne","last_name":"Caleno","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1963-05-11\",\"nhs\":\"************\",\"address\":\"49 Second Avenue CM1 4 ET\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM1 4ET\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-16 10:58:08"} 
[2025-07-22 10:37:17] local.ERROR: Failed to migrate user ID 991: Session store not set on request. {"ID":"991","user_login":"Jean_UxL62E","user_email":"<EMAIL>","user_nicename":"jean_uxl62e","display_name":"Jean Batterbee","user_registered":"2025-06-25 11:12:35","first_name":"Jean","last_name":"Batterbee","phone":null,"basic_data":"{\"mobile_number\":\"07782841151\",\"dob\":\"1940-01-24\",\"nhs\":\"\",\"address\":\"Elm Cottage Berners Roding Ongar Essex CM50Te\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Ongar Health Centre\",\"registered_gp_address\":\"Ongar Health Centre , Ongar Essex\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-25 12:12:35"} 
[2025-07-22 10:37:17] local.ERROR: Failed to migrate user ID 309: Session store not set on request. {"ID":"309","user_login":"Jemma_iD9mRT","user_email":"<EMAIL>","user_nicename":"jemma_id9mrt","display_name":"Jemma Holland","user_registered":"2025-02-14 16:08:14","first_name":"Jemma","last_name":"Holland","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1993-02-19\",\"nhs\":null,\"address\":\"31 Bounderby Grove, CM1 4XN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-14 16:08:14"} 
[2025-07-22 10:37:18] local.ERROR: Failed to migrate user ID 850: Session store not set on request. {"ID":"850","user_login":"Jennifer_I5GB1D","user_email":"<EMAIL>","user_nicename":"jennifer_i5gb1d","display_name":"Jennifer Ede","user_registered":"2025-05-28 14:31:23","first_name":"Jennifer","last_name":"Ede","phone":null,"basic_data":"{\"mobile_number\":\"07775431297\",\"gender\":\"female\",\"dob\":\"1957-07-09\",\"nhs\":null,\"address\":\"27 ANDERSON AVE\",\"city\":null,\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 2BZ\",\"blood_group\":\"\",\"registered_gp_name\":\"CHELMER MEDICAL PARTNERSHIP\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-28 15:31:23"} 
[2025-07-22 10:37:18] local.ERROR: Failed to migrate user ID 723: Session store not set on request. {"ID":"723","user_login":"Jennifer_X7DPHW","user_email":"<EMAIL>","user_nicename":"jennifer_x7dphw","display_name":"Jennifer Garrison","user_registered":"2025-04-29 10:32:24","first_name":"Jennifer","last_name":"Garrison","phone":null,"basic_data":"{\"mobile_number\":\"07831786128\",\"gender\":\"female\",\"dob\":\"1954-04-08\",\"nhs\":null,\"address\":\"29 kerridge close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM611ZT\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-29 11:32:24"} 
[2025-07-22 10:37:18] local.ERROR: Failed to migrate user ID 883: Session store not set on request. {"ID":"883","user_login":"Jessica_taenXU","user_email":"<EMAIL>","user_nicename":"jessica_taenxu","display_name":"Jessica Davies","user_registered":"2025-06-03 16:15:11","first_name":"Jessica","last_name":"Davies","phone":null,"basic_data":"{\"mobile_number\":\"07931469048\",\"gender\":\"female\",\"dob\":\"1991-02-02\",\"nhs\":null,\"address\":\"fLAT16 , NO3 CUENAUGHT SQUARE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm1 1AU\",\"blood_group\":\"\",\"registered_gp_name\":\"rIVERMEADE GATE CM1 1TR\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-03 17:15:11"} 
[2025-07-22 10:37:18] local.ERROR: Failed to migrate user ID 873: Session store not set on request. {"ID":"873","user_login":"Jessica_uRTjPI","user_email":"<EMAIL>","user_nicename":"jessica_urtjpi","display_name":"Jessica Ellis","user_registered":"2025-06-02 11:07:01","first_name":"Jessica","last_name":"Ellis","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2009-03-25\",\"nhs\":\"\",\"address\":\"The barn back lane Pleshey Chelmsford Cm31hl\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Great Waltham surgery\",\"registered_gp_address\":\"Cm33ll\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-02 12:07:01"} 
[2025-07-22 10:37:18] local.ERROR: Failed to migrate user ID 656: Session store not set on request. {"ID":"656","user_login":"Jessica_YsbWRw","user_email":"<EMAIL>","user_nicename":"jessica_ysbwrw","display_name":"Jessica Yelland","user_registered":"2025-04-18 15:55:54","first_name":"Jessica","last_name":"Yelland","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2004-05-29\",\"nhs\":\"**********\",\"address\":\"Gaston Cottage, Gaston Green, Bishops Stortford, CM22 7QS\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Patel\",\"registered_gp_address\":\"Broomfields, Hatfield Heath, Bishops Stortford, Hertfordshire, CM22 7EH\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-18 16:55:54"} 
[2025-07-22 10:37:19] local.ERROR: Failed to migrate user ID 564: Session store not set on request. {"ID":"564","user_login":"Joanna_Lrx3jH","user_email":"<EMAIL>","user_nicename":"joanna_lrx3jh","display_name":"Joanna Bultitude","user_registered":"2025-04-02 09:42:25","first_name":"Joanna","last_name":"Bultitude","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1977-09-13\",\"nhs\":\"**********\",\"address\":\"5 Kelvedon Close\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"20 Merlin Place, CM1 4HW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-02 10:42:25"} 
[2025-07-22 10:37:19] local.ERROR: Failed to migrate user ID 414: Session store not set on request. {"ID":"414","user_login":"Joanne_WiBF0S","user_email":"<EMAIL>","user_nicename":"joanne_wibf0s","display_name":"Joanne Bowman","user_registered":"2025-03-07 11:02:29","first_name":"Joanne","last_name":"Bowman","phone":null,"basic_data":"{\"mobile_number\":\"07712441025\",\"gender\":\"female\",\"dob\":\"1959-07-22\",\"nhs\":null,\"address\":\"53 Glebe Crescent, Broomfield, Chelmsford CM17BH\",\"city\":null,\"state\":\"\",\"country\":\"GB\",\"postal_code\":\"CM17BH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-27 10:05:54"} 
[2025-07-22 10:37:19] local.ERROR: Failed to migrate user ID 962: Session store not set on request. {"ID":"962","user_login":"Joanne_hmaiN4","user_email":"<EMAIL>","user_nicename":"joanne_hmain4","display_name":"Joanne Howden","user_registered":"2025-06-19 08:19:44","first_name":"Joanne","last_name":"Howden","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1969-12-06\",\"nhs\":\"**********\",\"address\":\"17 philip road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Any Available\",\"registered_gp_address\":\"Fern House Surgery, witham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-19 09:19:44"} 
[2025-07-22 10:37:19] local.ERROR: Failed to migrate user ID 965: Session store not set on request. {"ID":"965","user_login":"Joanne_f5UBDI","user_email":"<EMAIL>","user_nicename":"joanne_f5ubdi","display_name":"Joanne Marshall","user_registered":"2025-06-19 14:53:33","first_name":"Joanne","last_name":"Marshall","phone":null,"basic_data":"{\"mobile_number\":\"07910454712\",\"gender\":\"female\",\"dob\":\"1948-11-29\",\"nhs\":null,\"address\":\"25 Tarragon close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CO5 0SP\",\"blood_group\":\"\",\"registered_gp_name\":\"Tiptree Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-21 14:20:38"} 
[2025-07-22 10:37:19] local.ERROR: Failed to migrate user ID 954: Session store not set on request. {"ID":"954","user_login":"John_Wd5SeF","user_email":"<EMAIL>","user_nicename":"john_wd5sef","display_name":"John Allard","user_registered":"2025-06-17 14:34:23","first_name":"John","last_name":"Allard","phone":null,"basic_data":"{\"mobile_number\":\"07956196269\",\"gender\":\"male\",\"dob\":\"1956-05-30\",\"nhs\":null,\"address\":\"26 Derwent Ave Rayleigh\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS6 8LE\",\"blood_group\":\"\",\"registered_gp_name\":\"Dr Jenaka\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-17 15:34:23"} 
[2025-07-22 10:37:20] local.ERROR: Failed to migrate user ID 1023: Session store not set on request. {"ID":"1023","user_login":"John_8o3L2H","user_email":"<EMAIL>","user_nicename":"john_8o3l2h","display_name":"John Bush","user_registered":"2025-07-04 09:19:05","first_name":"John","last_name":"Bush","phone":null,"basic_data":"{\"mobile_number\":\"07957194988\",\"gender\":\"male\",\"dob\":\"1953-08-31\",\"nhs\":null,\"address\":\"2 Nicholas Court Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4XE\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 10:19:05"} 
[2025-07-22 10:37:20] local.ERROR: Failed to migrate user ID 455: Session store not set on request. {"ID":"455","user_login":"John_vl8Uw6","user_email":"<EMAIL>","user_nicename":"john_vl8uw6","display_name":"John Hartigan","user_registered":"2025-03-15 13:29:42","first_name":"John","last_name":"Hartigan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1954-06-16\",\"nhs\":\"\",\"address\":\"6 Marshalls Piece, Stebbing CM6 3RZ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Samatha Lowe\",\"registered_gp_address\":\"Freshwell Health Centre, Finchining field\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-15 13:29:42"} 
[2025-07-22 10:37:20] local.ERROR: Failed to migrate user ID 551: Session store not set on request. {"ID":"551","user_login":"John_cXUn6Q","user_email":"<EMAIL>","user_nicename":"john_cxun6q","display_name":"John Mason","user_registered":"2025-03-30 10:05:48","first_name":"John","last_name":"Mason","phone":null,"basic_data":"{\"mobile_number\":\"07488282223\",\"gender\":\"male\",\"dob\":\"1947-12-27\",\"nhs\":null,\"address\":null,\"city\":null,\"state\":\"\",\"country\":\"Wickham\",\"postal_code\":\"CM1~~\",\"blood_group\":\"\",\"registered_gp_name\":\"Sidney House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 11:33:39"} 
[2025-07-22 10:37:20] local.ERROR: Failed to migrate user ID 418: Session store not set on request. {"ID":"418","user_login":"John_F10tyB","user_email":"<EMAIL>","user_nicename":"john_f10tyb","display_name":"John McGeary","user_registered":"2025-03-08 11:47:05","first_name":"John","last_name":"McGeary","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1964-11-14\",\"nhs\":\"************\",\"address\":\"25 Celeborn Street South Woodham Ferrers\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Crouch Vale medical Centre\",\"registered_gp_address\":\"South woodham ferrers\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-08 11:47:05"} 
[2025-07-22 10:37:20] local.ERROR: Failed to migrate user ID 899: Session store not set on request. {"ID":"899","user_login":"Jonathan_zuIAHE","user_email":"<EMAIL>","user_nicename":"jonathan_zuiahe","display_name":"Jonathan Goodwin","user_registered":"2025-06-08 11:29:59","first_name":"Jonathan","last_name":"Goodwin","phone":null,"basic_data":"{\"mobile_number\":\"879776551\",\"gender\":\"male\",\"dob\":\"1988-11-24\",\"nhs\":null,\"address\":\"6 Deacon Drive\",\"city\":\"Basildon\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS15 5FY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-08 12:29:59"} 
[2025-07-22 10:37:21] local.ERROR: Failed to migrate user ID 858: Session store not set on request. {"ID":"858","user_login":"Jonathan_dGEn6P","user_email":"<EMAIL>","user_nicename":"jonathan_dgen6p","display_name":"Jonathan James","user_registered":"2025-05-31 05:57:10","first_name":"Jonathan","last_name":"James","phone":null,"basic_data":"{\"mobile_number\":\"97524424\",\"gender\":\"male\",\"dob\":\"1982-06-11\",\"nhs\":null,\"address\":\"2 Hoynors\",\"city\":\"Danbury\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 4RL\",\"blood_group\":\"\",\"registered_gp_name\":\"Jonathan james\",\"registered_gp_address\":\"Danbury\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 14:31:29"} 
[2025-07-22 10:37:21] local.ERROR: Failed to migrate user ID 645: Session store not set on request. {"ID":"645","user_login":"Jonathan_Pn0wjO","user_email":"<EMAIL>","user_nicename":"jonathan_pn0wjo","display_name":"Jonathan Player","user_registered":"2025-04-16 15:55:40","first_name":"Jonathan","last_name":"Player","phone":null,"basic_data":"{\"mobile_number\":\"07720550545\",\"dob\":\"1988-11-20\",\"nhs\":\"\",\"address\":\"Stanfields farm, Witham Road, white notley, cm81sd\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Silver end surgery\",\"registered_gp_address\":\"Silver end\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-16 16:55:40"} 
[2025-07-22 10:37:21] local.ERROR: Failed to migrate user ID 708: Session store not set on request. {"ID":"708","user_login":"Josef_yQPHI6","user_email":"<EMAIL>","user_nicename":"josef_yqphi6","display_name":"Josef Tennison-collins","user_registered":"2025-04-25 16:44:13","first_name":"Josef","last_name":"Tennison-collins","phone":null,"basic_data":"{\"mobile_number\":\"07711377549\",\"gender\":\"male\",\"dob\":\"1991-08-19\",\"nhs\":null,\"address\":\"Sunnyside , King Street, Essex\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM95DY\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-25 17:44:13"} 
[2025-07-22 10:37:21] local.ERROR: Failed to migrate user ID 587: Session store not set on request. {"ID":"587","user_login":"Josephine_ULMhFD","user_email":"<EMAIL>","user_nicename":"josephine_ulmhfd","display_name":"Josephine Train","user_registered":"2025-04-07 10:59:58","first_name":"Josephine","last_name":"Train","phone":null,"basic_data":"{\"mobile_number\":\"07908809264\",\"gender\":\"female\",\"dob\":\"1948-06-01\",\"nhs\":null,\"address\":\"Flat 20 Rosebury Court, Hutton, CM13 1BP\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 1BP\",\"blood_group\":\"\",\"registered_gp_name\":\"Rockly Court Brentwood\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 11:59:58"} 
[2025-07-22 10:37:21] local.ERROR: Failed to migrate user ID 828: Session store not set on request. {"ID":"828","user_login":"Joshua_LNysPA","user_email":"<EMAIL>","user_nicename":"joshua_lnyspa","display_name":"Joshua Holland","user_registered":"2025-05-24 09:19:36","first_name":"Joshua","last_name":"Holland","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"male\",\"dob\":\"1991-12-14\",\"nhs\":null,\"address\":\"31 Bounderby Grove\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4XN\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-24 10:19:36"} 
[2025-07-22 10:37:22] local.ERROR: Failed to migrate user ID 832: Session store not set on request. {"ID":"832","user_login":"Julie_0exa9W","user_email":"<EMAIL>","user_nicename":"julie_0exa9w","display_name":"Julie Dean","user_registered":"2025-05-24 11:24:21","first_name":"Julie","last_name":"Dean","phone":null,"basic_data":"{\"mobile_number\":\"07985521524\",\"gender\":\"female\",\"dob\":\"1964-05-13\",\"nhs\":null,\"address\":\"125 Mildmay Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 0DS\",\"blood_group\":\"\",\"registered_gp_name\":\"Beauchamp House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-24 12:24:21"} 
[2025-07-22 10:37:22] local.ERROR: Failed to migrate user ID 640: Session store not set on request. {"ID":"640","user_login":"Kamala_Ogyc4C","user_email":"<EMAIL>","user_nicename":"kamala_ogyc4c","display_name":"Kamala Peddasomayajula","user_registered":"2025-04-15 13:50:08","first_name":"Kamala","last_name":"Peddasomayajula","phone":null,"basic_data":"{\"mobile_number\":\"07900817276\",\"gender\":\"female\",\"dob\":\"1945-05-04\",\"nhs\":null,\"address\":\"24 Emberson Croft\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14FD\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-15 14:50:08"} 
[2025-07-22 10:37:22] local.ERROR: Failed to migrate user ID 292: Session store not set on request. {"ID":"292","user_login":"Karen_7ycj1a","user_email":"<EMAIL>","user_nicename":"karen_7ycj1a","display_name":"Karen Twitchett","user_registered":"2025-02-11 15:54:33","first_name":"Karen","last_name":"Twitchett","phone":null,"basic_data":"{\"mobile_number\":\"07913063337\",\"gender\":\"female\",\"dob\":\"1965-04-19\",\"nhs\":null,\"address\":\"79,Ockleford ave,Chelmsford cm1 2aw\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":\"united kingdom\",\"postal_code\":\"cm1 2aw\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-11 15:54:33"} 
[2025-07-22 10:37:22] local.ERROR: Failed to migrate user ID 417: Session store not set on request. {"ID":"417","user_login":"Karen_Rk3cNL","user_email":"<EMAIL>","user_nicename":"karen_rk3cnl","display_name":"Karen Woodley","user_registered":"2025-03-07 17:35:57","first_name":"Karen","last_name":"Woodley","phone":null,"basic_data":"{\"mobile_number\":\"07930218179\",\"gender\":\"female\",\"dob\":\"1959-09-11\",\"nhs\":null,\"address\":\"36 Madeline Place, Newland Spring, Chelmsford Essex, CM14XD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14XD\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-28 10:33:19"} 
[2025-07-22 10:37:22] local.ERROR: Failed to migrate user ID 552: Session store not set on request. {"ID":"552","user_login":"Katarzyna_VrBSwy","user_email":"<EMAIL>","user_nicename":"katarzyna_vrbswy","display_name":"Katarzyna Fraczek","user_registered":"2025-03-30 10:14:36","first_name":"Katarzyna","last_name":"Fraczek","phone":null,"basic_data":"{\"mobile_number\":\"07443647867\",\"dob\":\"1981-10-13\",\"nhs\":\"**********\",\"address\":\"2 Haig Court Chelmsford Cm2 0bh\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Sutherland Lodge\",\"registered_gp_address\":\"115 Baddow Road cm2 7py\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-30 11:14:36"} 
[2025-07-22 10:43:52] local.ERROR: Failed to migrate user ID 826: Session store not set on request. {"ID":"826","user_login":"Kathleen_FWVTxL","user_email":"<EMAIL>","user_nicename":"kathleen_fwvtxl","display_name":"Kathleen Skipp","user_registered":"2025-05-23 16:25:50","first_name":"Kathleen","last_name":"Skipp","phone":null,"basic_data":"{\"mobile_number\":\"07500554690\",\"gender\":\"female\",\"dob\":\"1983-11-03\",\"nhs\":null,\"address\":\"Adelaide Cottage, Swallows Cross, Brentwood Essex\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"Cm15 0SS\",\"blood_group\":\"\",\"registered_gp_name\":\"Diltree centre4\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-23 17:25:50"} 
[2025-07-22 10:43:52] local.ERROR: Failed to migrate user ID 507: Session store not set on request. {"ID":"507","user_login":"Kayleigh_t8SWsP","user_email":"<EMAIL>","user_nicename":"kayleigh_t8swsp","display_name":"Kayleigh Kruse","user_registered":"2025-03-23 22:42:26","first_name":"Kayleigh","last_name":"Kruse","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1994-11-30\",\"nhs\":\"\",\"address\":\"19 Spalding Avenue\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Melbourne house surgery\",\"registered_gp_address\":\"Melbourne avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 22:42:26"} 
[2025-07-22 10:43:53] local.ERROR: Failed to migrate user ID 989: Session store not set on request. {"ID":"989","user_login":"Keiko_9DuVqj","user_email":"<EMAIL>","user_nicename":"keiko_9duvqj","display_name":"Keiko Kato-warburg","user_registered":"2025-06-24 17:40:25","first_name":"Keiko","last_name":"Kato-warburg","phone":null,"basic_data":"{\"mobile_number\":\"02089072122\",\"gender\":\"female\",\"dob\":\"1959-07-01\",\"nhs\":null,\"address\":\"12 Gerdard Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"HA1 2ND\",\"blood_group\":\"\",\"registered_gp_name\":\"Northwick surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-24 18:40:25"} 
[2025-07-22 10:43:53] local.ERROR: Failed to migrate user ID 819: Session store not set on request. {"ID":"819","user_login":"kelly_jnv6V5","user_email":"<EMAIL>","user_nicename":"kelly_jnv6v5","display_name":"kelly lazaro","user_registered":"2025-05-21 13:12:50","first_name":"kelly","last_name":"lazaro","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1997-06-25\",\"nhs\":\"\",\"address\":\"4 cook place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Chelmer village sugary\",\"registered_gp_address\":\"4 cook place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-21 14:12:50"} 
[2025-07-22 10:43:54] local.ERROR: Failed to migrate user ID 456: Session store not set on request. {"ID":"456","user_login":"Kelly_trov6u","user_email":"<EMAIL>","user_nicename":"kelly_trov6u","display_name":"Kelly Sorrell","user_registered":"2025-03-15 21:27:04","first_name":"Kelly","last_name":"Sorrell","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1975-05-20\",\"nhs\":\"**********\",\"address\":\"53 Jeffcut Road Chelmsford Cm2 6xn\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Zeinab Al-Hilali\",\"registered_gp_address\":\"White Hart Lane, North Springfield, chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-15 21:27:04"} 
[2025-07-22 10:43:54] local.ERROR: Failed to migrate user ID 576: Session store not set on request. {"ID":"576","user_login":"Kerry_BxgEjm","user_email":"<EMAIL>","user_nicename":"kerry_bxgejm","display_name":"Kerry Sarris","user_registered":"2025-04-06 07:43:53","first_name":"Kerry","last_name":"Sarris","phone":null,"basic_data":"{\"mobile_number\":\"07526984683\",\"dob\":\"1988-04-15\",\"nhs\":\"\",\"address\":\"5 springmead Braintree Cm777px\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Na\",\"registered_gp_address\":\"Na\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 08:43:53"} 
[2025-07-22 10:43:55] local.ERROR: Failed to migrate user ID 542: Session store not set on request. {"ID":"542","user_login":"Kevin_RdJcTy","user_email":"<EMAIL>","user_nicename":"kevin_rdjcty","display_name":"Kevin Moss","user_registered":"2025-03-28 15:49:11","first_name":"Kevin","last_name":"Moss","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1962-02-10\",\"nhs\":null,\"address\":\"7 Langdale\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Great Notley gp\",\"registered_gp_address\":\"7 Langdale\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-29 11:31:02"} 
[2025-07-22 10:43:55] local.ERROR: Failed to migrate user ID 911: Session store not set on request. {"ID":"911","user_login":"Kevin_b24EUe","user_email":"<EMAIL>","user_nicename":"kevin_b24eue","display_name":"Kevin Murphy","user_registered":"2025-06-10 12:35:50","first_name":"Kevin","last_name":"Murphy","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1963-12-14\",\"nhs\":\"\",\"address\":\"Bellhouse, High Street, Stebbing, CM6 3SG\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"John Tasker House\",\"registered_gp_address\":\"New Road, Great Dunmow, CM6\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-10 13:35:50"} 
[2025-07-22 10:43:55] local.ERROR: Failed to migrate user ID 627: Session store not set on request. {"ID":"627","user_login":"Kevin_TsSjMp","user_email":"<EMAIL>","user_nicename":"kevin_tssjmp","display_name":"Kevin Ndango","user_registered":"2025-04-13 10:00:08","first_name":"Kevin","last_name":"Ndango","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1986-04-03\",\"nhs\":null,\"address\":\"14 Boundary Lane, Hampton Water\",\"city\":\"Peterborough\",\"state\":\"\",\"country\":\"United Kingdon\",\"postal_code\":\"PE7 8SL\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 11:00:08"} 
[2025-07-22 10:43:55] local.ERROR: Failed to migrate user ID 209: Session store not set on request. {"ID":"209","user_login":"Kevin_smyth_GsX8lx","user_email":"<EMAIL>","user_nicename":"kevin_smyth_gsx8lx","display_name":"<NAME_EMAIL>","user_registered":"2025-01-22 16:11:27","first_name":"Kevin smyth","last_name":"<EMAIL>","phone":null,"basic_data":"{\"mobile_number\":\"613236783\",\"dob\":\"1963-01-18\",\"nhs\":\"\",\"address\":\"53 Mendip Road Chelmsford cm1 2hn\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM1 2hn\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-22 16:11:27"} 
[2025-07-22 10:43:56] local.ERROR: Failed to migrate user ID 597: Session store not set on request. {"ID":"597","user_login":"Kieran_oqGd7C","user_email":"<EMAIL>","user_nicename":"kieran_oqgd7c","display_name":"Kieran Webb","user_registered":"2025-04-09 09:35:26","first_name":"Kieran","last_name":"Webb","phone":null,"basic_data":"{\"mobile_number\":\"07960510565\",\"gender\":\"male\",\"dob\":\"2003-02-26\",\"nhs\":null,\"address\":\"6 Medway Close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12LH\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-09 10:35:26"} 
[2025-07-22 10:43:56] local.ERROR: Failed to migrate user ID 976: Session store not set on request. {"ID":"976","user_login":"Kirsten_yQj8zP","user_email":"<EMAIL>","user_nicename":"kirsten_yqj8zp","display_name":"Kirsten Haugen","user_registered":"2025-06-21 10:35:33","first_name":"Kirsten","last_name":"Haugen","phone":null,"basic_data":"{\"mobile_number\":\"07961249381\",\"dob\":\"1940-02-21\",\"nhs\":\"\",\"address\":\"Flat 2 Armstrong Gibbs court, the causeway, great baddow Chelmsford cm2 7fr\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Baddow village surgery\",\"registered_gp_address\":\"Longmead avenue, Chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-21 11:35:33"} 
[2025-07-22 10:43:56] local.ERROR: Failed to migrate user ID 1022: Session store not set on request. {"ID":"1022","user_login":"Kokila_YVfajW","user_email":"<EMAIL>","user_nicename":"kokila_yvfajw","display_name":"Kokila Balu","user_registered":"2025-07-04 09:06:26","first_name":"Kokila","last_name":"Balu","phone":null,"basic_data":"{\"mobile_number\":\"07440120389\",\"gender\":\"female\",\"dob\":\"1962-09-26\",\"nhs\":null,\"address\":\"1 Bryony Close, Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4FZ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 10:06:26"} 
[2025-07-22 10:43:56] local.ERROR: Failed to migrate user ID 581: Session store not set on request. {"ID":"581","user_login":"Kristian_M5nVNw","user_email":"<EMAIL>","user_nicename":"kristian_m5nvnw","display_name":"Kristian Hotson","user_registered":"2025-04-06 10:46:04","first_name":"Kristian","last_name":"Hotson","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1982-11-07\",\"nhs\":\"\",\"address\":\"23 Buckthorn road, Great Dunmow, Essex, Cm6 1hz\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Jenns\",\"registered_gp_address\":\"South street bishops stortford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 11:46:04"} 
[2025-07-22 10:43:57] local.ERROR: Failed to migrate user ID 804: Session store not set on request. {"ID":"804","user_login":"Lachlan_Bgzv1V","user_email":"<EMAIL>","user_nicename":"lachlan_bgzv1v","display_name":"Lachlan Jones","user_registered":"2025-05-18 14:32:06","first_name":"Lachlan","last_name":"Jones","phone":null,"basic_data":"{\"mobile_number\":\"07706151420\",\"gender\":\"male\",\"dob\":\"2021-02-15\",\"nhs\":null,\"address\":\"The Oaks, The Gables, North Fambridge\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 6LN\",\"blood_group\":\"\",\"registered_gp_name\":\"Blackwater medical Centre- Maldon\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 15:32:06"} 
[2025-07-22 10:43:57] local.ERROR: Failed to migrate user ID 615: Session store not set on request. {"ID":"615","user_login":"Last_ZoMHfO","user_email":"<EMAIL>","user_nicename":"last_zomhfo","display_name":"Last Kanye","user_registered":"2025-04-11 18:12:52","first_name":"Last","last_name":"Kanye","phone":null,"basic_data":"{\"mobile_number\":\"452588797\",\"gender\":\"male\",\"dob\":\"1978-04-11\",\"nhs\":null,\"address\":\"Court Road, Chelmsford CM1 7ET\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 7ET\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 19:12:52"} 
[2025-07-22 10:43:57] local.ERROR: Failed to migrate user ID 258: Session store not set on request. {"ID":"258","user_login":"Lauren_JSrzk8","user_email":"<EMAIL>","user_nicename":"lauren_jsrzk8","display_name":"Lauren Yarnall","user_registered":"2025-02-04 12:17:42","first_name":"Lauren","last_name":"Yarnall","phone":null,"basic_data":"{\"mobile_number\":\"07547573006\",\"gender\":\"female\",\"dob\":\"2001-02-01\",\"nhs\":\"Whittle surgery\",\"address\":\"oak view farmbridge end road good easter\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14BB\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-28 13:41:50"} 
[2025-07-22 10:43:58] local.ERROR: Failed to migrate user ID 805: Session store not set on request. {"ID":"805","user_login":"Lee_3A1zVD","user_email":"<EMAIL>","user_nicename":"lee_3a1zvd","display_name":"Lee London","user_registered":"2025-05-18 19:33:21","first_name":"Lee","last_name":"London","phone":null,"basic_data":"{\"mobile_number\":\"07792015543\",\"dob\":\"1968-04-27\",\"nhs\":\"\",\"address\":\"20 Peggotty Close Chelmsford Essex, CM1 4XU\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Booth\",\"registered_gp_address\":\"Parkside Medical Centre, Melbourne Ave, Chelmsford CM1 2DY\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 20:33:21"} 
[2025-07-22 10:43:58] local.ERROR: Failed to migrate user ID 937: Session store not set on request. {"ID":"937","user_login":"Leigh_HyErou","user_email":"<EMAIL>","user_nicename":"leigh_hyerou","display_name":"Leigh Johnson","user_registered":"2025-06-14 22:08:54","first_name":"Leigh","last_name":"Johnson","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1974-01-22\",\"nhs\":null,\"address\":\"30 wilshire avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"River mead gate\",\"registered_gp_address\":\"Chelmsford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-15 13:21:52"} 
[2025-07-22 10:43:58] local.ERROR: Failed to migrate user ID 1016: Session store not set on request. {"ID":"1016","user_login":"Leila_ZqJ7Fe","user_email":"<EMAIL>","user_nicename":"leila_zqj7fe","display_name":"Leila White","user_registered":"2025-06-30 17:10:11","first_name":"Leila","last_name":"White","phone":null,"basic_data":"{\"mobile_number\":\"07961828936\",\"gender\":\"female\",\"dob\":\"1956-10-07\",\"nhs\":null,\"address\":\"The Haven Roxwell Rd\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3SA\",\"blood_group\":\"\",\"registered_gp_name\":\"Ongar Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 18:10:11"} 
[2025-07-22 10:43:59] local.ERROR: Failed to migrate user ID 332: Session store not set on request. {"ID":"332","user_login":"Leo_mTHWLf","user_email":"<EMAIL>","user_nicename":"leo_mthwlf","display_name":"Leo Ashenden","user_registered":"2025-02-21 11:30:20","first_name":"Leo","last_name":"Ashenden","phone":null,"basic_data":"{\"mobile_number\":\"07522879735\",\"gender\":\"male\",\"dob\":\"2020-08-09\",\"nhs\":null,\"address\":\"6 Beaulieu Boulevard, Chelmsford, CM1 6EA\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-21 11:30:20"} 
[2025-07-22 10:44:00] local.ERROR: Failed to migrate user ID 340: Session store not set on request. {"ID":"340","user_login":"Leon_CtYepN","user_email":"<EMAIL>","user_nicename":"leon_ctyepn","display_name":"Leon Grant","user_registered":"2025-02-23 12:25:49","first_name":"Leon","last_name":"Grant","phone":null,"basic_data":"{\"mobile_number\":\"07980619030\",\"gender\":\"male\",\"dob\":\"1982-02-13\",\"nhs\":null,\"address\":\"15 Well Lane, Chelmsford, CM2 8QY\",\"city\":null,\"state\":\"\",\"country\":\"uk\",\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-06 18:07:24"} 
[2025-07-22 10:44:00] local.ERROR: Failed to migrate user ID 968: Session store not set on request. {"ID":"968","user_login":"Lewis_yqzQcE","user_email":"<EMAIL>","user_nicename":"lewis_yqzqce","display_name":"Lewis Weaver","user_registered":"2025-06-19 19:14:51","first_name":"Lewis","last_name":"Weaver","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1991-08-11\",\"nhs\":\"**********\",\"address\":\"90 school lane, CM17DS\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr heyes\",\"registered_gp_address\":\"The Shrubberies Medical Centre 12 The Shrubberies George Lane London E18 1BD\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-19 20:14:51"} 
[2025-07-22 10:44:00] local.ERROR: Failed to migrate user ID 679: Session store not set on request. {"ID":"679","user_login":"Lily-May_OLzDKk","user_email":"<EMAIL>","user_nicename":"lily-may_olzdkk","display_name":"Lily-May Plume","user_registered":"2025-04-23 07:49:17","first_name":"Lily-May","last_name":"Plume","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1999-03-23\",\"nhs\":\"************\",\"address\":\"64 westlea road, en10 6jd\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Dr wood\",\"registered_gp_address\":\"Park lane surgery\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-08 10:18:31"} 
[2025-07-22 10:44:01] local.ERROR: Failed to migrate user ID 306: Session store not set on request. {"ID":"306","user_login":"Linda_N0vjKM","user_email":"<EMAIL>","user_nicename":"linda_n0vjkm","display_name":"Linda Gilmour","user_registered":"2025-02-13 18:46:28","first_name":"Linda","last_name":"Gilmour","phone":null,"basic_data":"{\"mobile_number\":\"07779841246\",\"gender\":\"female\",\"dob\":\"1956-09-01\",\"nhs\":null,\"address\":\"36 Havisham way\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14UY\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-16 09:54:01"} 
[2025-07-22 10:44:01] local.ERROR: Failed to migrate user ID 726: Session store not set on request. {"ID":"726","user_login":"LINDSAY_pibvYr","user_email":"<EMAIL>","user_nicename":"lindsay_pibvyr","display_name":"LINDSAY BUNNEY","user_registered":"2025-04-29 13:00:31","first_name":"LINDSAY","last_name":"BUNNEY","phone":null,"basic_data":"{\"mobile_number\":\"07762420877\",\"gender\":\"female\",\"dob\":\"1979-05-30\",\"nhs\":null,\"address\":\"46 Voysey Garden\",\"city\":\"Basildon\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS131QS\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-29 14:00:31"} 
[2025-07-22 10:44:01] local.ERROR: Failed to migrate user ID 486: Session store not set on request. {"ID":"486","user_login":"Lindsay_l9Cawn","user_email":"<EMAIL>","user_nicename":"lindsay_l9cawn","display_name":"Lindsay Havercroft","user_registered":"2025-03-21 09:51:25","first_name":"Lindsay","last_name":"Havercroft","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1985-12-31\",\"nhs\":null,\"address\":\"6 Elder Close , Chelmsford, CM1 4FU\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4FU\",\"blood_group\":\"\",\"registered_gp_name\":\"Parkside Medical Centre\",\"registered_gp_address\":\"Mother of George Havercroft\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 09:53:21"} 
[2025-07-22 10:44:01] local.ERROR: Failed to migrate user ID 713: Session store not set on request. {"ID":"713","user_login":"Lisa_bwm68N","user_email":"<EMAIL>","user_nicename":"lisa_bwm68n","display_name":"Lisa Ingrouille","user_registered":"2025-04-26 13:43:47","first_name":"Lisa","last_name":"Ingrouille","phone":null,"basic_data":"{\"mobile_number\":\"07906025585\",\"gender\":\"female\",\"dob\":\"1972-12-20\",\"nhs\":null,\"address\":\"5 Haversham Way, CM1 4UY\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4UY\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-26 14:43:48"} 
[2025-07-22 10:44:02] local.ERROR: Failed to migrate user ID 978: Session store not set on request. {"ID":"978","user_login":"Lisa_tes9ya","user_email":"<EMAIL>","user_nicename":"lisa_tes9ya","display_name":"Lisa Revell","user_registered":"2025-06-21 10:55:17","first_name":"Lisa","last_name":"Revell","phone":null,"basic_data":"{\"mobile_number\":\"07383554182\",\"dob\":\"1983-11-21\",\"nhs\":\"\",\"address\":\"Garnish Hall Chelmsford rd Margaret Roding CM6 1QL\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Eden Surgery\",\"registered_gp_address\":\"Hatfield Heath\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-21 11:55:17"} 
[2025-07-22 10:44:02] local.ERROR: Failed to migrate user ID 837: Session store not set on request. {"ID":"837","user_login":"Lisa_30NG8w","user_email":"<EMAIL>","user_nicename":"lisa_30ng8w","display_name":"Lisa Thomas","user_registered":"2025-05-25 13:01:52","first_name":"Lisa","last_name":"Thomas","phone":null,"basic_data":"{\"mobile_number\":\"07885218152\",\"gender\":\"female\",\"dob\":\"1988-06-15\",\"nhs\":null,\"address\":\"3A Halsted Road\",\"city\":\"Braintree\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM7 5PB\",\"blood_group\":\"\",\"registered_gp_name\":\"Blyde's Meadow\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-25 14:01:52"} 
[2025-07-22 10:44:02] local.ERROR: Failed to migrate user ID 784: Session store not set on request. {"ID":"784","user_login":"Lorna_cXTWsi","user_email":"<EMAIL>","user_nicename":"lorna_cxtwsi","display_name":"Lorna Hawkes","user_registered":"2025-05-15 12:19:09","first_name":"Lorna","last_name":"Hawkes","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1996-09-15\",\"nhs\":\"\",\"address\":\"1 Azalea Mews Felsted Dunmow CM6 3YW\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Brasse\",\"registered_gp_address\":\"John Tasker GP\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-15 13:19:09"} 
[2025-07-22 10:44:02] local.ERROR: Failed to migrate user ID 781: Session store not set on request. {"ID":"781","user_login":"Lorraine_vjLMek","user_email":"<EMAIL>","user_nicename":"lorraine_vjlmek","display_name":"Lorraine Pilime","user_registered":"2025-05-14 08:16:28","first_name":"Lorraine","last_name":"Pilime","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1983-05-25\",\"nhs\":\"\",\"address\":\"Flat 1, 63 Queensland Crescent, Chelmsford, CM12DZ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Melbourne Road Practice\",\"registered_gp_address\":\"Melbourne Avenue, Chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-14 09:16:28"} 
[2025-07-22 10:44:03] local.ERROR: Failed to migrate user ID 808: Session store not set on request. {"ID":"808","user_login":"Lucy_Qsrczm","user_email":"<EMAIL>","user_nicename":"lucy_qsrczm","display_name":"Lucy Barton","user_registered":"2025-05-19 11:17:59","first_name":"Lucy","last_name":"Barton","phone":null,"basic_data":"{\"mobile_number\":\"07519083874\",\"gender\":\"female\",\"dob\":\"2001-07-19\",\"nhs\":null,\"address\":\"207 Church st, Braintree\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM75LH\",\"blood_group\":\"\",\"registered_gp_name\":\"Church Lane Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-19 17:53:54"} 
[2025-07-22 10:44:03] local.ERROR: Failed to migrate user ID 266: Session store not set on request. {"ID":"266","user_login":"Luis_Alfredo_UrAaXb","user_email":"<EMAIL>","user_nicename":"luis_alfredo_uraaxb","display_name":"Luis Alfredo Filizzola Arzuaga","user_registered":"2025-02-08 08:58:46","first_name":"Luis Alfredo","last_name":"Filizzola Arzuaga","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1986-06-08\",\"nhs\":\"**********\",\"address\":\"51 Greens farm lane Billericay CM112hz\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"Cm112hz\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-08 08:58:46"} 
[2025-07-22 10:44:03] local.ERROR: Failed to migrate user ID 811: Session store not set on request. {"ID":"811","user_login":"Luis_BVpvtx","user_email":"<EMAIL>","user_nicename":"luis_bvpvtx","display_name":"Luis Ferreira","user_registered":"2025-05-19 13:50:45","first_name":"Luis","last_name":"Ferreira","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1987-12-05\",\"nhs\":\"************\",\"address\":\"5 Roslings Cl, CM1 2HA\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Chelmer Medical Partnership - Tennyson House Surgery\",\"registered_gp_address\":\"20 Merlin Place, CM1 4HW\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-19 14:50:45"} 
[2025-07-22 10:44:04] local.ERROR: Failed to migrate user ID 754: Session store not set on request. {"ID":"754","user_login":"Lydia_oGgDv4","user_email":"<EMAIL>","user_nicename":"lydia_oggdv4","display_name":"Lydia Evans","user_registered":"2025-05-08 12:26:14","first_name":"Lydia","last_name":"Evans","phone":null,"basic_data":"{\"mobile_number\":\"07894661465\",\"gender\":\"female\",\"dob\":\"1949-11-30\",\"nhs\":null,\"address\":\"53 Ockleford Ave\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1ZAW\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-08 13:26:14"} 
[2025-07-22 10:44:04] local.ERROR: Failed to migrate user ID 806: Session store not set on request. {"ID":"806","user_login":"Lynda_4jHWSD","user_email":"<EMAIL>","user_nicename":"lynda_4jhwsd","display_name":"Lynda Ellen","user_registered":"2025-05-19 09:00:53","first_name":"Lynda","last_name":"Ellen","phone":null,"basic_data":"{\"mobile_number\":\"07881821133\",\"gender\":\"female\",\"dob\":\"1955-12-21\",\"nhs\":null,\"address\":\"18 Battle Rise, Haybridge\",\"city\":\"Maldon\",\"state\":\"\",\"country\":\"Essex\",\"postal_code\":\"CM9 4PF\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-19 10:00:53"} 
[2025-07-22 10:44:05] local.ERROR: Failed to migrate user ID 245: Session store not set on request. {"ID":"245","user_login":"Lynn_TtCyIu","user_email":"<EMAIL>","user_nicename":"lynn_ttcyiu","display_name":"Lynn Parsell","user_registered":"2025-02-02 12:08:49","first_name":"Lynn","last_name":"Parsell","phone":null,"basic_data":"{\"mobile_number\":\"07906483362\",\"gender\":\"female\",\"dob\":\"1956-09-12\",\"nhs\":null,\"address\":\"Flat 8, Belvedere Court\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM2 0BT\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-02 12:08:49"} 
[2025-07-22 10:44:05] local.ERROR: Failed to migrate user ID 488: Session store not set on request. {"ID":"488","user_login":"Lynne_4F0TDz","user_email":"<EMAIL>","user_nicename":"lynne_4f0tdz","display_name":"Lynne Oscroft","user_registered":"2025-03-21 10:07:19","first_name":"Lynne","last_name":"Oscroft","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1946-06-23\",\"nhs\":null,\"address\":\"4 The Manor House , Roxwell Rd\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3RY\",\"blood_group\":\"\",\"registered_gp_name\":\"Lordship Road Writtle\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 10:07:19"} 
[2025-07-22 10:44:05] local.ERROR: Failed to migrate user ID 544: Session store not set on request. {"ID":"544","user_login":"Madison_lKpnAu","user_email":"<EMAIL>","user_nicename":"madison_lkpnau","display_name":"Madison Woodard","user_registered":"2025-03-29 09:39:56","first_name":"Madison","last_name":"Woodard","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2003-01-26\",\"nhs\":\"************\",\"address\":\"44 Hudson Road SS9 5NX\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Zhadi\",\"registered_gp_address\":\"Eastwood Group Practice Rayleigh Road SS9 5PU\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-29 09:39:56"} 
[2025-07-22 10:44:06] local.ERROR: Failed to migrate user ID 916: Session store not set on request. {"ID":"916","user_login":"Marc_E1iYtu","user_email":"<EMAIL>","user_nicename":"marc_e1iytu","display_name":"Marc Kershaw","user_registered":"2025-06-11 11:43:11","first_name":"Marc","last_name":"Kershaw","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1994-02-10\",\"nhs\":\"\",\"address\":\"14 Manilla Road, Southend on Sea SS1 2FB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Valkyrie Doctors Surgery\",\"registered_gp_address\":\"Westcliff on Sea\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-11 12:43:11"} 
[2025-07-22 10:44:06] local.ERROR: Failed to migrate user ID 592: Session store not set on request. {"ID":"592","user_login":"Margaret_Pat_EfduIo","user_email":"<EMAIL>","user_nicename":"margaret_pat_efduio","display_name":"Margaret Pat Melvin","user_registered":"2025-04-08 10:34:08","first_name":"Margaret Pat","last_name":"Melvin","phone":null,"basic_data":"{\"mobile_number\":\"07901664229\",\"gender\":\"female\",\"dob\":\"1944-03-17\",\"nhs\":null,\"address\":\"6 Coastone Place\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12YT\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-08 11:52:26"} 
[2025-07-22 10:44:06] local.ERROR: Failed to migrate user ID 1000: Session store not set on request. {"ID":"1000","user_login":"Margaret_3FNP6i","user_email":"<EMAIL>","user_nicename":"margaret_3fnp6i","display_name":"Margaret Richardson","user_registered":"2025-06-27 14:49:58","first_name":"Margaret","last_name":"Richardson","phone":null,"basic_data":"{\"mobile_number\":\"01277653753\",\"gender\":\"female\",\"dob\":\"1940-08-09\",\"nhs\":null,\"address\":\"4 Holley Gardens, Billericay\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM11 1AJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Billericay Health Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-27 15:49:58"} 
[2025-07-22 10:44:07] local.ERROR: Failed to migrate user ID 843: Session store not set on request. {"ID":"843","user_login":"Margret_qSN0WP","user_email":"<EMAIL>","user_nicename":"margret_qsn0wp","display_name":"Margret Madge","user_registered":"2025-05-27 09:13:09","first_name":"Margret","last_name":"Madge","phone":null,"basic_data":"{\"mobile_number\":\"07742489319\",\"gender\":\"female\",\"dob\":\"1936-03-29\",\"nhs\":null,\"address\":\"55 Rox well Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2LE\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermade gate , Dr.Singh\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-27 18:37:20"} 
[2025-07-22 10:44:07] local.ERROR: Failed to migrate user ID 305: Session store not set on request. {"ID":"305","user_login":"Marharyta_dnJbw0","user_email":"<EMAIL>","user_nicename":"marharyta_dnjbw0","display_name":"Marharyta Bohdalova","user_registered":"2025-02-13 17:25:32","first_name":"Marharyta","last_name":"Bohdalova","phone":null,"basic_data":"{\"mobile_number\":\"07437603439\",\"gender\":\"female\",\"dob\":\"1991-02-06\",\"nhs\":null,\"address\":\"233 Nevebdon Road\",\"city\":\"Wickford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"SS12 0PY\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-13 17:25:32"} 
[2025-07-22 10:44:08] local.ERROR: Failed to migrate user ID 395: Session store not set on request. {"ID":"395","user_login":"Maria_fiuJhG","user_email":"<EMAIL>","user_nicename":"maria_fiujhg","display_name":"Maria Moore","user_registered":"2025-03-04 10:58:16","first_name":"Maria","last_name":"Moore","phone":null,"basic_data":"{\"mobile_number\":\"07896877904\",\"gender\":\"female\",\"dob\":\"1980-10-07\",\"nhs\":null,\"address\":\"Thymeside, CM13RD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM13RD\",\"blood_group\":\"\",\"registered_gp_name\":\"Writtle House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-04 10:58:16"} 
[2025-07-22 10:44:08] local.ERROR: Failed to migrate user ID 454: Session store not set on request. {"ID":"454","user_login":"Marie_ABERC7","user_email":"<EMAIL>","user_nicename":"marie_aberc7","display_name":"Marie Allen","user_registered":"2025-03-15 10:43:06","first_name":"Marie","last_name":"Allen","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1976-02-21\",\"nhs\":null,\"address\":\"65 Lowefield, Earl's Cone CO6 2LH\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CO6 2LH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-15 10:43:06"} 
[2025-07-22 10:44:09] local.ERROR: Failed to migrate user ID 934: Session store not set on request. {"ID":"934","user_login":"Marilyn_g1zNYV","user_email":"<EMAIL>","user_nicename":"marilyn_g1znyv","display_name":"Marilyn Reid","user_registered":"2025-06-14 10:23:12","first_name":"Marilyn","last_name":"Reid","phone":null,"basic_data":"{\"mobile_number\":\"07887607717\",\"gender\":\"female\",\"dob\":\"1939-06-20\",\"nhs\":\"**********\",\"address\":\"7 Quinion Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4UH\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne Avenue\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-14 11:23:12"} 
[2025-07-22 10:44:09] local.ERROR: Failed to migrate user ID 624: Session store not set on request. {"ID":"624","user_login":"Mark_James_Q8a51K","user_email":"<EMAIL>","user_nicename":"mark_james_q8a51k","display_name":"Mark James Buckland","user_registered":"2025-04-12 12:59:53","first_name":"Mark James","last_name":"Buckland","phone":null,"basic_data":"{\"mobile_number\":\"07503980507\",\"gender\":\"male\",\"dob\":\"1966-08-27\",\"nhs\":null,\"address\":\"Averley Clears Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CO4 9XT\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 13:59:53"} 
[2025-07-22 10:44:09] local.ERROR: Failed to migrate user ID 907: Session store not set on request. {"ID":"907","user_login":"Mark_6kpWsD","user_email":"<EMAIL>","user_nicename":"mark_6kpwsd","display_name":"Mark Rhule","user_registered":"2025-06-09 12:56:14","first_name":"Mark","last_name":"Rhule","phone":null,"basic_data":"{\"mobile_number\":\"07943603310\",\"gender\":\"male\",\"dob\":\"1976-08-26\",\"nhs\":null,\"address\":\"2 White Hart Road\",\"city\":\"Plumsted\",\"state\":\"\",\"country\":null,\"postal_code\":\"SE18 3AQ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-09 13:56:14"} 
[2025-07-22 10:44:10] local.ERROR: Failed to migrate user ID 886: Session store not set on request. {"ID":"886","user_login":"Martin_x4qfbn","user_email":"<EMAIL>","user_nicename":"martin_x4qfbn","display_name":"Martin Greenwood","user_registered":"2025-06-05 11:02:39","first_name":"Martin","last_name":"Greenwood","phone":null,"basic_data":"{\"mobile_number\":\"07976625188\",\"gender\":\"male\",\"dob\":\"1966-06-05\",\"nhs\":null,\"address\":\"5 MULTON LEA, CHELMSFORD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 6EF\",\"blood_group\":\"\",\"registered_gp_name\":\"Sidney House and the laurels\",\"registered_gp_address\":\"Strutt Close Hatfield Pevesal CM 3 2HB\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-05 13:26:02"} 
[2025-07-22 10:44:10] local.ERROR: Failed to migrate user ID 441: Session store not set on request. {"ID":"441","user_login":"Martin_zW8lrJ","user_email":"<EMAIL>","user_nicename":"martin_zw8lrj","display_name":"Martin Skeel","user_registered":"2025-03-12 12:55:19","first_name":"Martin","last_name":"Skeel","phone":null,"basic_data":"{\"mobile_number\":\"07905798311\",\"gender\":\"male\",\"dob\":\"1942-09-12\",\"nhs\":null,\"address\":\"3 Broomfield Mews\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4GA\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennison House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 12:15:38"} 
[2025-07-22 10:44:10] local.ERROR: Failed to migrate user ID 763: Session store not set on request. {"ID":"763","user_login":"Martin_mrSWRk","user_email":"<EMAIL>","user_nicename":"martin_mrswrk","display_name":"Martin Southgate","user_registered":"2025-05-10 11:08:44","first_name":"Martin","last_name":"Southgate","phone":null,"basic_data":"{\"mobile_number\":\"07791448623\",\"dob\":\"1989-07-16\",\"nhs\":\"\",\"address\":\"2 Barleyfield Drive Weeley Co16 9dw\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Great Bentley\",\"registered_gp_address\":\"Great Bentley\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-10 12:08:45"} 
[2025-07-22 10:44:10] local.ERROR: Failed to migrate user ID 438: Session store not set on request. {"ID":"438","user_login":"Mary_f3po2E","user_email":"<EMAIL>","user_nicename":"mary_f3po2e","display_name":"Mary Welham","user_registered":"2025-03-12 09:52:09","first_name":"Mary","last_name":"Welham","phone":null,"basic_data":"{\"mobile_number\":\"07783039636\",\"gender\":\"female\",\"dob\":\"1952-11-20\",\"nhs\":null,\"address\":\"17 North Dell Springfield\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-12 09:52:09"} 
[2025-07-22 10:44:11] local.ERROR: Failed to migrate user ID 495: Session store not set on request. {"ID":"495","user_login":"Mateo_ckFhGt","user_email":"<EMAIL>","user_nicename":"mateo_ckfhgt","display_name":"Mateo Vega","user_registered":"2025-03-22 10:21:06","first_name":"Mateo","last_name":"Vega","phone":null,"basic_data":"{\"mobile_number\":\"07984538600\",\"gender\":\"male\",\"dob\":\"2018-01-01\",\"nhs\":null,\"address\":\"326 Linnet Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 8AL\",\"blood_group\":\"\",\"registered_gp_name\":\"Baddow Village Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 10:21:06"} 
[2025-07-22 10:44:11] local.ERROR: Failed to migrate user ID 995: Session store not set on request. {"ID":"995","user_login":"Matthew_M3fCKu","user_email":"<EMAIL>","user_nicename":"matthew_m3fcku","display_name":"Matthew Brace","user_registered":"2025-06-26 13:34:48","first_name":"Matthew","last_name":"Brace","phone":null,"basic_data":"{\"mobile_number\":\"07894862476\",\"gender\":\"male\",\"dob\":\"2007-11-15\",\"nhs\":null,\"address\":\"Willowside, east hanning road, Howe green\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 7TQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Sutherland Lodge Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-26 14:34:48"} 
[2025-07-22 10:44:11] local.ERROR: Failed to migrate user ID 944: Session store not set on request. {"ID":"944","user_login":"Matthew_3wefLb","user_email":"<EMAIL>","user_nicename":"matthew_3weflb","display_name":"Matthew Smith","user_registered":"2025-06-16 10:05:34","first_name":"Matthew","last_name":"Smith","phone":null,"basic_data":"{\"mobile_number\":\"07592858331\",\"dob\":\"1999-07-05\",\"nhs\":\"\",\"address\":\"31 Victoria Road Newtongrange EH22 4NN\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Alison Binns\",\"registered_gp_address\":\"Blackcot drive, Mayfield, EH22 4AA\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-16 11:05:34"} 
[2025-07-22 10:44:11] local.ERROR: Failed to migrate user ID 545: Session store not set on request. {"ID":"545","user_login":"Matthew_1oeRAU","user_email":"<EMAIL>","user_nicename":"matthew_1oerau","display_name":"Matthew Tunstill","user_registered":"2025-03-29 09:40:25","first_name":"Matthew","last_name":"Tunstill","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1986-12-29\",\"nhs\":\"\",\"address\":\"22 Wood Street, Chelmsford, Essex, CM2 9AS\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Whitley House Surgery\",\"registered_gp_address\":\"Whitley House Surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-29 09:40:25"} 
[2025-07-22 10:44:12] local.ERROR: Failed to migrate user ID 357: Session store not set on request. {"ID":"357","user_login":"Maureen_YviU6c","user_email":"<EMAIL>","user_nicename":"maureen_yviu6c","display_name":"Maureen Fitch","user_registered":"2025-02-26 12:21:21","first_name":"Maureen","last_name":"Fitch","phone":null,"basic_data":"{\"mobile_number\":\"07876699496\",\"gender\":\"female\",\"dob\":\"1948-07-06\",\"nhs\":null,\"address\":\"13 Hopping Jacks Lane, Danbury CM34PN\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Beacon health group Danbury\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-27 19:24:11"} 
[2025-07-22 10:44:12] local.ERROR: Failed to migrate user ID 699: Session store not set on request. {"ID":"699","user_login":"Maureen_lhy0tf","user_email":"<EMAIL>","user_nicename":"maureen_lhy0tf","display_name":"Maureen Morrison","user_registered":"2025-04-24 13:54:39","first_name":"Maureen","last_name":"Morrison","phone":null,"basic_data":"{\"mobile_number\":\"01277353774\",\"gender\":\"female\",\"dob\":\"1938-04-13\",\"nhs\":null,\"address\":\"8 Ridqeway\",\"city\":\"Ingatestone\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM49AS\",\"blood_group\":\"\",\"registered_gp_name\":\"New Fully Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-24 15:21:14"} 
[2025-07-22 10:44:12] local.ERROR: Failed to migrate user ID 933: Session store not set on request. {"ID":"933","user_login":"Mavis_x3T5u7","user_email":"<EMAIL>","user_nicename":"mavis_x3t5u7","display_name":"Mavis Butler","user_registered":"2025-06-14 09:44:24","first_name":"Mavis","last_name":"Butler","phone":null,"basic_data":"{\"mobile_number\":\"01245263299\",\"gender\":\"female\",\"dob\":\"1953-01-21\",\"nhs\":null,\"address\":\"19 Great Cobb\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 6LA\",\"blood_group\":\"\",\"registered_gp_name\":\"Humber Road, Springfield\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-14 10:44:24"} 
[2025-07-22 10:44:12] local.ERROR: Failed to migrate user ID 1014: Session store not set on request. {"ID":"1014","user_login":"Max_HDojsV","user_email":"<EMAIL>","user_nicename":"max_hdojsv","display_name":"Max Thomas","user_registered":"2025-06-30 14:56:03","first_name":"Max","last_name":"Thomas","phone":null,"basic_data":"{\"mobile_number\":\"07940006727\",\"gender\":\"male\",\"dob\":\"2008-07-10\",\"nhs\":null,\"address\":\"55 Berwick Avenue Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4AW\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 15:56:03"} 
[2025-07-22 10:44:13] local.ERROR: Failed to migrate user ID 365: Session store not set on request. {"ID":"365","user_login":"Melanie_Jayne_bY9ech","user_email":"<EMAIL>","user_nicename":"melanie_jayne_by9ech","display_name":"Melanie Jayne Casey","user_registered":"2025-02-27 13:28:13","first_name":"Melanie Jayne","last_name":"Casey","phone":null,"basic_data":"{\"mobile_number\":\"07495448743\",\"gender\":\"female\",\"dob\":\"1960-07-27\",\"nhs\":\"********** (Tennyson House)\",\"address\":\"1 Ashford Place\",\"city\":\"Broomfield\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM17FW\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"Melbourne\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-27 15:12:40"} 
[2025-07-22 10:44:13] local.ERROR: Failed to migrate user ID 892: Session store not set on request. {"ID":"892","user_login":"mia_Rokp7n","user_email":"<EMAIL>","user_nicename":"mia_rokp7n","display_name":"mia mounsey","user_registered":"2025-06-06 13:02:30","first_name":"mia","last_name":"mounsey","phone":null,"basic_data":"{\"mobile_number\":\"07860109943\",\"dob\":\"2008-10-31\",\"nhs\":\"\",\"address\":\"84 east park harlow cm17 0sb\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Jegerov\",\"registered_gp_address\":\"jenner house, old harlow CM17\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-06 14:02:30"} 
[2025-07-22 10:44:13] local.ERROR: Failed to migrate user ID 492: Session store not set on request. {"ID":"492","user_login":"Michael_fUaXiQ","user_email":"<EMAIL>","user_nicename":"michael_fuaxiq","display_name":"Michael Barrett","user_registered":"2025-03-21 15:06:01","first_name":"Michael","last_name":"Barrett","phone":null,"basic_data":"{\"mobile_number\":\"07359347696\",\"gender\":\"male\",\"dob\":\"1979-05-25\",\"nhs\":null,\"address\":\"10 Crushton Place\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14WH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 15:06:01"} 
[2025-07-22 10:44:13] local.ERROR: Failed to migrate user ID 506: Session store not set on request. {"ID":"506","user_login":"Michael_YQnv0y","user_email":"<EMAIL>","user_nicename":"michael_yqnv0y","display_name":"Michael Bowkett","user_registered":"2025-03-23 15:06:16","first_name":"Michael","last_name":"Bowkett","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1963-09-01\",\"nhs\":null,\"address\":\"123\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"123\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 15:06:16"} 
[2025-07-22 10:44:14] local.ERROR: Failed to migrate user ID 214: Session store not set on request. {"ID":"214","user_login":"Michael_7hHMLt","user_email":"<EMAIL>","user_nicename":"michael_7hhmlt","display_name":"Michael Carter","user_registered":"2025-01-24 10:51:31","first_name":"Michael","last_name":"Carter","phone":null,"basic_data":"{\"mobile_number\":\"07585117591\",\"gender\":\"male\",\"dob\":\"1952-11-01\",\"nhs\":null,\"address\":\"89 Globe Crescent\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM17BM\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-29 10:59:59"} 
[2025-07-22 10:44:14] local.ERROR: Failed to migrate user ID 344: Session store not set on request. {"ID":"344","user_login":"Michael_hxnM36","user_email":"<EMAIL>","user_nicename":"michael_hxnm36","display_name":"Michael Ellis","user_registered":"2025-02-24 14:11:28","first_name":"Michael","last_name":"Ellis","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1964-09-30\",\"nhs\":null,\"address\":\"6 Spriggs oak, Epping, Essex , CM166SE\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-24 14:11:28"} 
[2025-07-22 10:44:15] local.ERROR: Failed to migrate user ID 790: Session store not set on request. {"ID":"790","user_login":"Michael_Nyx0Zm","user_email":"<EMAIL>","user_nicename":"michael_nyx0zm","display_name":"Michael Hyde","user_registered":"2025-05-16 09:10:20","first_name":"Michael","last_name":"Hyde","phone":null,"basic_data":"{\"mobile_number\":\"07767792201\",\"dob\":\"1959-06-01\",\"nhs\":\"\",\"address\":\"36 Juniper Rd Boreham Chelmsford CM3 3DB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Siddiq\",\"registered_gp_address\":\"Sydney House &amp; The Laurels Hatfield Peveral\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-16 10:10:20"} 
[2025-07-22 10:44:15] local.ERROR: Failed to migrate user ID 412: Session store not set on request. {"ID":"412","user_login":"Michelle_I8J6Us","user_email":"<EMAIL>","user_nicename":"michelle_i8j6us","display_name":"Michelle Whipps","user_registered":"2025-03-07 07:52:25","first_name":"Michelle","last_name":"Whipps","phone":null,"basic_data":"{\"mobile_number\":\"07802814399\",\"dob\":\"1968-08-13\",\"nhs\":\"**********\",\"address\":\"Greenfields, Chignal Smealy Chelmsford Essex CM1 4SU\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"dont know\",\"registered_gp_address\":\"Little Waltham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-07 07:52:25"} 
[2025-07-22 10:44:15] local.ERROR: Failed to migrate user ID 349: Session store not set on request. {"ID":"349","user_login":"Migration_X2_LFGbms","user_email":"<EMAIL>","user_nicename":"migration_x2_lfgbms","display_name":"Migration X2 Test","user_registered":"2025-02-25 17:02:40","first_name":"Migration X2","last_name":"Test","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-15\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-25 17:02:40"} 
[2025-07-22 10:44:15] local.ERROR: Failed to migrate user ID 350: Session store not set on request. {"ID":"350","user_login":"Migration_X2_FMiYlb","user_email":"<EMAIL>","user_nicename":"migration_x2_fmiylb","display_name":"Migration X2 Test","user_registered":"2025-02-25 17:03:56","first_name":"Migration X2","last_name":"Test","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-21\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-25 17:03:56"} 
[2025-07-22 10:44:16] local.ERROR: Failed to migrate user ID 351: Session store not set on request. {"ID":"351","user_login":"Migration1_8hCYzp","user_email":"<EMAIL>","user_nicename":"migration1_8hcyzp","display_name":"Migration1 Majety","user_registered":"2025-02-25 17:06:37","first_name":"Migration1","last_name":"Majety","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-15\",\"nhs\":\"\",\"address\":\"50 Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Test\",\"registered_gp_address\":\"50 Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-25 17:06:37"} 
[2025-07-22 10:44:16] local.ERROR: Failed to migrate user ID 565: Session store not set on request. {"ID":"565","user_login":"Millie_kToAdt","user_email":"<EMAIL>","user_nicename":"millie_ktoadt","display_name":"Millie Cockerell","user_registered":"2025-04-02 11:39:44","first_name":"Millie","last_name":"Cockerell","phone":null,"basic_data":"{\"mobile_number\":\"07925416729\",\"gender\":\"female\",\"dob\":\"2006-03-01\",\"nhs\":null,\"address\":\"197 London Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SS69DN\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-08 10:51:37"} 
[2025-07-22 10:44:16] local.ERROR: Failed to migrate user ID 602: Session store not set on request. {"ID":"602","user_login":"Mireille_tA3dkQ","user_email":"<EMAIL>","user_nicename":"mireille_ta3dkq","display_name":"Mireille Carr","user_registered":"2025-04-10 09:09:26","first_name":"Mireille","last_name":"Carr","phone":null,"basic_data":"{\"mobile_number\":\"07565130370\",\"gender\":\"female\",\"dob\":\"1940-12-09\",\"nhs\":null,\"address\":\"4 Homefield close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12HE\",\"blood_group\":\"\",\"registered_gp_name\":\"Parkside Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-29 17:04:04"} 
[2025-07-22 10:44:17] local.ERROR: Failed to migrate user ID 928: Session store not set on request. {"ID":"928","user_login":"Mohammed_Basil_RviILX","user_email":"<EMAIL>","user_nicename":"mohammed_basil_rviilx","display_name":"Mohammed Basil Attar","user_registered":"2025-06-13 16:53:11","first_name":"Mohammed Basil","last_name":"Attar","phone":null,"basic_data":"{\"mobile_number\":\"07446271835\",\"gender\":\"male\",\"dob\":\"2001-04-02\",\"nhs\":null,\"address\":\"6 Fullwood\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"PR2 7ES\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-13 17:53:11"} 
[2025-07-22 10:44:17] local.ERROR: Failed to migrate user ID 972: Session store not set on request. {"ID":"972","user_login":"Molly_RM9KpE","user_email":"<EMAIL>","user_nicename":"molly_rm9kpe","display_name":"Molly Osborn","user_registered":"2025-06-20 11:08:14","first_name":"Molly","last_name":"Osborn","phone":null,"basic_data":"{\"mobile_number\":\"07446957836\",\"gender\":\"female\",\"dob\":\"1996-04-19\",\"nhs\":null,\"address\":\"Mill House\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3ET\",\"blood_group\":\"\",\"registered_gp_name\":\"Writtle Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-20 12:08:14"} 
[2025-07-22 10:44:17] local.ERROR: Failed to migrate user ID 990: Session store not set on request. {"ID":"990","user_login":"Monique_H9VSvG","user_email":"<EMAIL>","user_nicename":"monique_h9vsvg","display_name":"Monique Cates","user_registered":"2025-06-25 10:38:40","first_name":"Monique","last_name":"Cates","phone":null,"basic_data":"{\"mobile_number\":\"07818028447\",\"dob\":\"1981-05-27\",\"nhs\":\"************\",\"address\":\"2 Vicerons place Bishops Stortford Herts Cm23 4el\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr burtan\",\"registered_gp_address\":\"Church street partnership, bishops Stortford, herts\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-25 11:38:40"} 
[2025-07-22 10:44:18] local.ERROR: Failed to migrate user ID 857: Session store not set on request. {"ID":"857","user_login":"Mrs_Jill_IOvFWx","user_email":"<EMAIL>","user_nicename":"mrs_jill_iovfwx","display_name":"Mrs Jill Bruns","user_registered":"2025-05-30 19:17:07","first_name":"Mrs Jill","last_name":"Bruns","phone":null,"basic_data":"{\"mobile_number\":\"07828606092\",\"dob\":\"1981-04-03\",\"nhs\":\"\",\"address\":\"13 derwent court Hobart close Chelmsford Cm1 2fn\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr booth\",\"registered_gp_address\":\"Melbourne house surgery Chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-30 20:17:07"} 
[2025-07-22 10:44:18] local.ERROR: Failed to migrate user ID 308: Session store not set on request. {"ID":"308","user_login":"Myles_pxYVEw","user_email":"<EMAIL>","user_nicename":"myles_pxyvew","display_name":"Myles Threader","user_registered":"2025-02-14 14:42:52","first_name":"Myles","last_name":"Threader","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2000-05-01\",\"nhs\":\"\",\"address\":\"390 Springfield Road CM2 6AT\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM26AT\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-14 14:42:52"} 
[2025-07-22 10:44:19] local.ERROR: Failed to migrate user ID 1029: Session store not set on request. {"ID":"1029","user_login":"Naomi_GaQt7l","user_email":"<EMAIL>","user_nicename":"naomi_gaqt7l","display_name":"Naomi Day","user_registered":"2025-07-04 18:48:22","first_name":"Naomi","last_name":"Day","phone":null,"basic_data":"{\"mobile_number\":\"07909535723\",\"dob\":\"1979-01-19\",\"nhs\":\"\",\"address\":\"12a Seagers, Gt Totham\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Fowler\",\"registered_gp_address\":\"Tiptree medical centre\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 19:48:22"} 
[2025-07-22 10:44:19] local.ERROR: Failed to migrate user ID 844: Session store not set on request. {"ID":"844","user_login":"Naresh_wHM5GY","user_email":"<EMAIL>","user_nicename":"naresh_whm5gy","display_name":"Naresh Mal","user_registered":"2025-05-27 09:31:56","first_name":"Naresh","last_name":"Mal","phone":null,"basic_data":"{\"mobile_number\":\"07920884339\",\"gender\":\"male\",\"dob\":\"1983-12-24\",\"nhs\":null,\"address\":\"33 high new hall\",\"city\":\"Harllow\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM17 9FA\",\"blood_group\":\"\",\"registered_gp_name\":\"Church langley\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-27 10:31:56"} 
[2025-07-22 10:44:20] local.ERROR: Failed to migrate user ID 435: Session store not set on request. {"ID":"435","user_login":"Natalia_geB86z","user_email":"<EMAIL>","user_nicename":"natalia_geb86z","display_name":"Natalia Doloi","user_registered":"2025-03-11 17:16:10","first_name":"Natalia","last_name":"Doloi","phone":null,"basic_data":"{\"mobile_number\":\"07308517062\",\"gender\":\"female\",\"dob\":\"2019-01-12\",\"nhs\":null,\"address\":\"2 Headgate Avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-11 17:16:10"} 
[2025-07-22 10:44:20] local.ERROR: Failed to migrate user ID 618: Session store not set on request. {"ID":"618","user_login":"Nathan_3CyeBi","user_email":"<EMAIL>","user_nicename":"nathan_3cyebi","display_name":"Nathan Jenner","user_registered":"2025-04-12 08:16:46","first_name":"Nathan","last_name":"Jenner","phone":null,"basic_data":"{\"mobile_number\":\"07595411308\",\"dob\":\"1975-12-06\",\"nhs\":\"\",\"address\":\"Hobblefield Hornells corner Chelmsford CM3 1QW\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Little Waltham surgery\",\"registered_gp_address\":\"Little Waltham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 09:16:46"} 
[2025-07-22 10:44:20] local.ERROR: Failed to migrate user ID 870: Session store not set on request. {"ID":"870","user_login":"Niamh_0zloFx","user_email":"<EMAIL>","user_nicename":"niamh_0zlofx","display_name":"Niamh Griggs","user_registered":"2025-06-01 13:46:45","first_name":"Niamh","last_name":"Griggs","phone":null,"basic_data":"{\"mobile_number\":\"07956680725\",\"gender\":\"female\",\"dob\":\"2007-04-14\",\"nhs\":null,\"address\":\"13 Penine Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2HG\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 14:46:45"} 
[2025-07-22 10:44:21] local.ERROR: Failed to migrate user ID 264: Session store not set on request. {"ID":"264","user_login":"Nichola_n9Y4jP","user_email":"<EMAIL>","user_nicename":"nichola_n9y4jp","display_name":"Nichola Almeida","user_registered":"2025-02-07 10:26:52","first_name":"Nichola","last_name":"Almeida","phone":null,"basic_data":"{\"mobile_number\":\"07949249591\",\"gender\":\"female\",\"dob\":\"1980-06-23\",\"nhs\":null,\"address\":\"The thatched cottage chignil road\",\"city\":null,\"state\":\"\",\"country\":\"CHELMSFORD\",\"postal_code\":\"CM14SZ\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-07 10:26:52"} 
[2025-07-22 10:44:21] local.ERROR: Failed to migrate user ID 1019: Session store not set on request. {"ID":"1019","user_login":"Nicola_RusaeF","user_email":"<EMAIL>","user_nicename":"nicola_rusaef","display_name":"Nicola Brocklebank","user_registered":"2025-07-02 14:43:06","first_name":"Nicola","last_name":"Brocklebank","phone":null,"basic_data":"{\"mobile_number\":\"***********\",\"gender\":\"female\",\"dob\":\"1975-02-04\",\"nhs\":null,\"address\":\"42 Norfold Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"RM14 2Rf\",\"blood_group\":\"\",\"registered_gp_name\":\"Dr Tran\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-02 15:46:13"} 
[2025-07-22 10:44:22] local.ERROR: Failed to migrate user ID 590: Session store not set on request. {"ID":"590","user_login":"Nicola_UOWNs3","user_email":"<EMAIL>","user_nicename":"nicola_uowns3","display_name":"Nicola Mee","user_registered":"2025-04-07 11:41:35","first_name":"Nicola","last_name":"Mee","phone":null,"basic_data":"{\"mobile_number\":\"***********\",\"gender\":\"female\",\"dob\":\"1986-02-25\",\"nhs\":null,\"address\":\"26 Paric Avenue\",\"city\":\"Chlemsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM12AA\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 12:41:35"} 
[2025-07-22 10:44:22] local.ERROR: Failed to migrate user ID 575: Session store not set on request. {"ID":"575","user_login":"Nicola_UETamQ","user_email":"<EMAIL>","user_nicename":"nicola_uetamq","display_name":"Nicola Oshea","user_registered":"2025-04-05 12:50:13","first_name":"Nicola","last_name":"Oshea","phone":null,"basic_data":"{\"mobile_number\":\"07852960078\",\"gender\":\"female\",\"dob\":\"1984-06-12\",\"nhs\":null,\"address\":\"7 Blackford Lane, Wickham Bishop\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM8 3NR\",\"blood_group\":\"\",\"registered_gp_name\":\"North Essex NHS GP\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-05 13:50:13"} 
[2025-07-22 10:44:22] local.ERROR: Failed to migrate user ID 621: Session store not set on request. {"ID":"621","user_login":"Nicola_ky7CI0","user_email":"<EMAIL>","user_nicename":"nicola_ky7ci0","display_name":"Nicola Young","user_registered":"2025-04-12 10:02:45","first_name":"Nicola","last_name":"Young","phone":null,"basic_data":"{\"mobile_number\":\"07958088738\",\"gender\":\"female\",\"dob\":\"1968-06-27\",\"nhs\":null,\"address\":\"43 Tapley Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4XY\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 11:02:45"} 
[2025-07-22 10:44:23] local.ERROR: Failed to migrate user ID 777: Session store not set on request. {"ID":"777","user_login":"Nieve_X9C4hO","user_email":"<EMAIL>","user_nicename":"nieve_x9c4ho","display_name":"Nieve Baker","user_registered":"2025-05-13 09:52:14","first_name":"Nieve","last_name":"Baker","phone":null,"basic_data":"{\"mobile_number\":\"07849130324\",\"gender\":\"female\",\"dob\":\"2001-12-05\",\"nhs\":null,\"address\":\"37 Woolmans Close broxbane\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"EN10 6PR\",\"blood_group\":\"\",\"registered_gp_name\":\"Parklane Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-13 10:52:14"} 
[2025-07-22 10:44:23] local.ERROR: Failed to migrate user ID 753: Session store not set on request. {"ID":"753","user_login":"Nina_LZezfs","user_email":"<EMAIL>","user_nicename":"nina_lzezfs","display_name":"Nina Daniel","user_registered":"2025-05-07 15:15:38","first_name":"Nina","last_name":"Daniel","phone":null,"basic_data":"{\"mobile_number\":\"07944523350\",\"gender\":\"female\",\"dob\":\"1981-10-12\",\"nhs\":null,\"address\":\"22 Danbury Palace Drive Danbury\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM34FA\",\"blood_group\":\"\",\"registered_gp_name\":\"Beacon Health Group\",\"registered_gp_address\":\"52 Maldon Road, Danbury, CM3 4QL\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-12 17:45:59"} 
[2025-07-22 10:44:23] local.ERROR: Failed to migrate user ID 440: Session store not set on request. {"ID":"440","user_login":"Nina_ROz7qH","user_email":"<EMAIL>","user_nicename":"nina_roz7qh","display_name":"Nina Walker","user_registered":"2025-03-12 11:23:14","first_name":"Nina","last_name":"Walker","phone":null,"basic_data":"{\"mobile_number\":\"07824393930\",\"gender\":\"female\",\"dob\":\"1973-09-07\",\"nhs\":null,\"address\":\"92 Hillside Grove CM29DB\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM29DB\",\"blood_group\":\"\",\"registered_gp_name\":\"Mousham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 12:29:17"} 
[2025-07-22 10:44:23] local.ERROR: Failed to migrate user ID 648: Session store not set on request. {"ID":"648","user_login":"Norman_YMayfX","user_email":"<EMAIL>","user_nicename":"norman_ymayfx","display_name":"Norman Smith","user_registered":"2025-04-17 16:04:25","first_name":"Norman","last_name":"Smith","phone":null,"basic_data":"{\"mobile_number\":\"01245440339\",\"gender\":\"male\",\"dob\":\"1944-10-20\",\"nhs\":null,\"address\":\"Dunelm, Mashbury Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14TZ\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-17 17:04:25"} 
[2025-07-22 10:44:23] local.ERROR: Failed to migrate user ID 677: Session store not set on request. {"ID":"677","user_login":"ojj96398@bcooq.com_a5xMBG","user_email":"<EMAIL>","user_nicename":"ojj96398bcooq-com_a5xmbg","display_name":"<EMAIL> <EMAIL>","user_registered":"2025-04-22 19:54:39","first_name":"<EMAIL>","last_name":"<EMAIL>","phone":null,"basic_data":"{\"mobile_number\":\"54654645654\",\"dob\":\"2025-03-31\",\"nhs\":\"987979\",\"address\":\"<EMAIL>\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"<EMAIL>\",\"registered_gp_address\":\"<EMAIL>\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 20:54:39"} 
[2025-07-22 10:44:24] local.ERROR: Failed to migrate user ID 500: Session store not set on request. {"ID":"500","user_login":"Olayinka_Vm8qr2","user_email":"<EMAIL>","user_nicename":"olayinka_vm8qr2","display_name":"Olayinka Tiamiyu","user_registered":"2025-03-23 08:15:46","first_name":"Olayinka","last_name":"Tiamiyu","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1989-06-11\",\"nhs\":\"\",\"address\":\"113 Wheatfield Way\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"113 Wheatfield Way\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-23 08:15:46"} 
[2025-07-22 10:44:24] local.ERROR: Failed to migrate user ID 864: Session store not set on request. {"ID":"864","user_login":"Olga_qRJbQc","user_email":"<EMAIL>","user_nicename":"olga_qrjbqc","display_name":"Olga Pazenuka","user_registered":"2025-05-31 18:12:29","first_name":"Olga","last_name":"Pazenuka","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1993-06-07\",\"nhs\":\"\",\"address\":\"112 Orchard Way Boreham\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"The Laurels Surgery\",\"registered_gp_address\":\"112 Orchard Way\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-31 19:12:29"} 
[2025-07-22 10:44:24] local.ERROR: Failed to migrate user ID 419: Session store not set on request. {"ID":"419","user_login":"Olive_zVuI3w","user_email":"<EMAIL>","user_nicename":"olive_zvui3w","display_name":"Olive Bailey","user_registered":"2025-03-08 13:05:25","first_name":"Olive","last_name":"Bailey","phone":null,"basic_data":"{\"mobile_number\":\"07776181251\",\"gender\":\"female\",\"dob\":\"1940-10-25\",\"nhs\":null,\"address\":\"16 Roslings Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2HA\",\"blood_group\":\"\",\"registered_gp_name\":\"Beauchamp House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-08 13:05:25"} 
[2025-07-22 10:44:24] local.ERROR: Failed to migrate user ID 672: Session store not set on request. {"ID":"672","user_login":"Oliver_t4qed8","user_email":"<EMAIL>","user_nicename":"oliver_t4qed8","display_name":"Oliver Readman","user_registered":"2025-04-22 11:18:57","first_name":"Oliver","last_name":"Readman","phone":null,"basic_data":"{\"mobile_number\":\"07502404884\",\"gender\":\"male\",\"dob\":\"2004-04-22\",\"nhs\":null,\"address\":\"Littley Park Farm, Littley Park Lane, CM3 1LB, Halstead\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1LB\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 12:18:57"} 
[2025-07-22 10:44:25] local.ERROR: Failed to migrate user ID 674: Session store not set on request. {"ID":"674","user_login":"Oliver_CToqUA","user_email":"<EMAIL>","user_nicename":"oliver_ctoqua","display_name":"Oliver Streek","user_registered":"2025-04-22 17:07:20","first_name":"Oliver","last_name":"Streek","phone":null,"basic_data":"{\"mobile_number\":\"07730289508\",\"gender\":\"male\",\"dob\":\"1978-05-06\",\"nhs\":null,\"address\":\"69 Pickwick Ave\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM14UR\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 18:07:20"} 
[2025-07-22 10:44:25] local.ERROR: Failed to migrate user ID 546: Session store not set on request. {"ID":"546","user_login":"Olivia_DiBGCH","user_email":"<EMAIL>","user_nicename":"olivia_dibgch","display_name":"Olivia Hopwood","user_registered":"2025-03-29 11:16:08","first_name":"Olivia","last_name":"Hopwood","phone":null,"basic_data":"{\"mobile_number\":\"07885611816\",\"dob\":\"2021-01-19\",\"nhs\":\"\",\"address\":\"8 Saint Michaels Mews Leaden Roding CM6 1QZ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Unknown\",\"registered_gp_address\":\"Eden surgery Hatfield Heath\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-29 11:16:08"} 
[2025-07-22 10:44:25] local.ERROR: Failed to migrate user ID 752: Session store not set on request. {"ID":"752","user_login":"Ondrej_li8wX3","user_email":"<EMAIL>","user_nicename":"ondrej_li8wx3","display_name":"Ondrej Benes","user_registered":"2025-05-07 09:29:37","first_name":"Ondrej","last_name":"Benes","phone":null,"basic_data":"{\"mobile_number\":\"608412037\",\"gender\":\"male\",\"dob\":\"2019-03-18\",\"nhs\":null,\"address\":\"Victoria Crescent\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM11QF\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-07 10:29:37"} 
[2025-07-22 10:44:25] local.ERROR: Failed to migrate user ID 742: Session store not set on request. {"ID":"742","user_login":"Paige_TMu5rV","user_email":"<EMAIL>","user_nicename":"paige_tmu5rv","display_name":"Paige Packer","user_registered":"2025-05-04 11:07:05","first_name":"Paige","last_name":"Packer","phone":null,"basic_data":"{\"mobile_number\":\"07368974897\",\"gender\":\"female\",\"dob\":\"2000-12-11\",\"nhs\":null,\"address\":\"43 West Ave\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2BB\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-04 12:07:05"} 
[2025-07-22 10:44:26] local.ERROR: Failed to migrate user ID 548: Session store not set on request. {"ID":"548","user_login":"Pamela_fBW23k","user_email":"<EMAIL>","user_nicename":"pamela_fbw23k","display_name":"Pamela Rason","user_registered":"2025-03-29 12:15:16","first_name":"Pamela","last_name":"Rason","phone":null,"basic_data":"{\"mobile_number\":\"07793824312\",\"gender\":\"female\",\"dob\":\"1945-08-24\",\"nhs\":null,\"address\":\"10 Noefolk Drive Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14AG\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-29 12:20:00"} 
[2025-07-22 10:44:26] local.ERROR: Failed to migrate user ID 741: Session store not set on request. {"ID":"741","user_login":"Patricia_erN7FY","user_email":"<EMAIL>","user_nicename":"patricia_ern7fy","display_name":"Patricia Giles","user_registered":"2025-05-04 11:04:59","first_name":"Patricia","last_name":"Giles","phone":null,"basic_data":"{\"mobile_number\":\"07831489109\",\"gender\":\"female\",\"dob\":\"1942-04-11\",\"nhs\":null,\"address\":\"49 Heron Way\",\"city\":\"Brentwood\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 2LQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Hutton Mount Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-05 12:42:24"} 
[2025-07-22 10:44:26] local.ERROR: Failed to migrate user ID 235: Session store not set on request. {"ID":"235","user_login":"Patricia_6aJ1xW","user_email":"<EMAIL>","user_nicename":"patricia_6aj1xw","display_name":"Patricia Quee","user_registered":"2025-01-31 10:36:53","first_name":"Patricia","last_name":"Quee","phone":null,"basic_data":"{\"mobile_number\":\"07801899139\",\"gender\":\"female\",\"dob\":\"1950-05-21\",\"nhs\":null,\"address\":\"36 Sowerberry close, chelmsford CM14YB\",\"city\":\"CHELMSFORD\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM14YB\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-31 10:36:54"} 
[2025-07-22 10:44:26] local.ERROR: Failed to migrate user ID 424: Session store not set on request. {"ID":"424","user_login":"Patricia_LP09Wh","user_email":"<EMAIL>","user_nicename":"patricia_lp09wh","display_name":"Patricia Walker","user_registered":"2025-03-09 11:30:22","first_name":"Patricia","last_name":"Walker","phone":null,"basic_data":"{\"mobile_number\":\"07775336083\",\"gender\":\"female\",\"dob\":\"1961-11-28\",\"nhs\":null,\"address\":\"38 Plantation Rd\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 3EA\",\"blood_group\":\"\",\"registered_gp_name\":\"Sydney House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-09 11:30:22"} 
[2025-07-22 10:44:27] local.ERROR: Failed to migrate user ID 765: Session store not set on request. {"ID":"765","user_login":"Patricia_Ox3X0b","user_email":"<EMAIL>","user_nicename":"patricia_ox3x0b","display_name":"Patricia Watson-Smith","user_registered":"2025-05-10 12:35:09","first_name":"Patricia","last_name":"Watson-Smith","phone":null,"basic_data":"{\"mobile_number\":\"07989043829\",\"gender\":\"female\",\"dob\":\"1939-01-20\",\"nhs\":null,\"address\":\"11 Barmead\",\"city\":\"Doddinghurst\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM15 0ND\",\"blood_group\":\"\",\"registered_gp_name\":\"Diltree\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-10 13:35:09"} 
[2025-07-22 10:44:27] local.ERROR: Failed to migrate user ID 589: Session store not set on request. {"ID":"589","user_login":"Patryk_hWOs29","user_email":"<EMAIL>","user_nicename":"patryk_hwos29","display_name":"Patryk Fraczek","user_registered":"2025-04-07 11:39:39","first_name":"Patryk","last_name":"Fraczek","phone":null,"basic_data":"{\"mobile_number\":\"07443647867\",\"gender\":\"male\",\"dob\":\"2011-03-03\",\"nhs\":null,\"address\":\"2 Haig Court\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM2ODH\",\"blood_group\":\"\",\"registered_gp_name\":\"Sutherland Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 12:39:39"} 
[2025-07-22 10:44:27] local.ERROR: Failed to migrate user ID 1012: Session store not set on request. {"ID":"1012","user_login":"Paul_jW3Jwq","user_email":"<EMAIL>","user_nicename":"paul_jw3jwq","display_name":"Paul Seyfang","user_registered":"2025-06-30 12:04:03","first_name":"Paul","last_name":"Seyfang","phone":null,"basic_data":"{\"mobile_number\":\"07836666892\",\"gender\":\"male\",\"dob\":\"1948-01-28\",\"nhs\":\"**********\",\"address\":\"9 little dorsit newlands spring, Chlemsford, Essex CM14YQ\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14YQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 13:05:12"} 
[2025-07-22 10:44:27] local.ERROR: Failed to migrate user ID 809: Session store not set on request. {"ID":"809","user_login":"Paul_QWCnw5","user_email":"<EMAIL>","user_nicename":"paul_qwcnw5","display_name":"Paul Southgate","user_registered":"2025-05-19 12:05:17","first_name":"Paul","last_name":"Southgate","phone":null,"basic_data":"{\"mobile_number\":\"07947143970\",\"gender\":\"male\",\"dob\":\"1968-11-20\",\"nhs\":null,\"address\":\"3 Norton Rd,\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2QP\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-19 13:05:17"} 
[2025-07-22 10:44:28] local.ERROR: Failed to migrate user ID 570: Session store not set on request. {"ID":"570","user_login":"Paul_iUY1sa","user_email":"<EMAIL>","user_nicename":"paul_iuy1sa","display_name":"Paul Stevens","user_registered":"2025-04-05 09:31:31","first_name":"Paul","last_name":"Stevens","phone":null,"basic_data":"{\"mobile_number\":\"07594537955\",\"gender\":\"male\",\"dob\":\"1960-02-22\",\"nhs\":null,\"address\":\"1 Sorrell Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 3LP\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-05 10:31:31"} 
[2025-07-22 10:44:28] local.ERROR: Failed to migrate user ID 491: Session store not set on request. {"ID":"491","user_login":"Paul_3CjZfL","user_email":"<EMAIL>","user_nicename":"paul_3cjzfl","display_name":"Paul Webber","user_registered":"2025-03-21 13:07:34","first_name":"Paul","last_name":"Webber","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1971-12-20\",\"nhs\":null,\"address\":\"38 New England Close , Bicknacre\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 4XA\",\"blood_group\":\"\",\"registered_gp_name\":\"Danbury Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 13:14:09"} 
[2025-07-22 10:44:28] local.ERROR: Failed to migrate user ID 430: Session store not set on request. {"ID":"430","user_login":"Pauline_akXmNQ","user_email":"<EMAIL>","user_nicename":"pauline_akxmnq","display_name":"Pauline Thomson","user_registered":"2025-03-10 08:19:07","first_name":"Pauline","last_name":"Thomson","phone":null,"basic_data":"{\"mobile_number\":\"07970467425\",\"dob\":\"1965-06-19\",\"nhs\":\"**********\",\"address\":\"1 Kingswood Court Braintree CM7 9FU\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Unknown\",\"registered_gp_address\":\"Church Lane Surgery, Braintree\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-10 08:19:08"} 
[2025-07-22 10:44:28] local.ERROR: Failed to migrate user ID 783: Session store not set on request. {"ID":"783","user_login":"Peggy_6hVESk","user_email":"<EMAIL>","user_nicename":"peggy_6hvesk","display_name":"Peggy Cooper","user_registered":"2025-05-15 09:21:45","first_name":"Peggy","last_name":"Cooper","phone":null,"basic_data":"{\"mobile_number\":\"07807950380\",\"gender\":\"female\",\"dob\":\"2015-05-24\",\"nhs\":null,\"address\":\"11 Trinity Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"C09 1EA\",\"blood_group\":\"\",\"registered_gp_name\":\"Elizabeth court house surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-22 15:16:19"} 
[2025-07-22 10:44:29] local.ERROR: Failed to migrate user ID 913: Session store not set on request. {"ID":"913","user_login":"Penelope_gLkoOX","user_email":"<EMAIL>","user_nicename":"penelope_glkoox","display_name":"Penelope Leigh","user_registered":"2025-06-10 14:00:00","first_name":"Penelope","last_name":"Leigh","phone":null,"basic_data":"{\"mobile_number\":\"01245260587\",\"gender\":\"female\",\"dob\":\"1948-03-15\",\"nhs\":null,\"address\":\"2, Parkland Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 7RJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Chelmer medical partnership\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-10 15:00:00"} 
[2025-07-22 10:44:29] local.ERROR: Failed to migrate user ID 849: Session store not set on request. {"ID":"849","user_login":"Peter_nt4o50","user_email":"peterclift123**@gmail.com","user_nicename":"peter_nt4o50","display_name":"Peter Clift","user_registered":"2025-05-28 14:18:19","first_name":"Peter","last_name":"Clift","phone":null,"basic_data":"{\"mobile_number\":\"01245443167\",\"gender\":\"male\",\"dob\":\"1946-09-22\",\"nhs\":null,\"address\":\"33 Mill Lane\",\"city\":\"Broomfield\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 7BQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Lt Weltham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-28 16:57:09"} 
[2025-07-22 10:44:29] local.ERROR: Failed to migrate user ID 317: Session store not set on request. {"ID":"317","user_login":"Peter_QrfWSC","user_email":"<EMAIL>","user_nicename":"peter_qrfwsc","display_name":"Peter Harpin","user_registered":"2025-02-15 10:07:26","first_name":"Peter","last_name":"Harpin","phone":null,"basic_data":"{\"mobile_number\":\"07899074429\",\"gender\":\"male\",\"dob\":\"1980-06-05\",\"nhs\":null,\"address\":\"51 Pytt Field\",\"city\":\"Harlow\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM17 9AA\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-15 10:39:13"} 
[2025-07-22 10:44:29] local.ERROR: Failed to migrate user ID 966: Session store not set on request. {"ID":"966","user_login":"Peter_7dOmP8","user_email":"<EMAIL>","user_nicename":"peter_7domp8","display_name":"Peter Willis","user_registered":"2025-06-19 15:36:04","first_name":"Peter","last_name":"Willis","phone":null,"basic_data":"{\"mobile_number\":\"000000000\",\"gender\":\"male\",\"dob\":\"1951-04-24\",\"nhs\":null,\"address\":\"31 Carstone Place\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 4YT\",\"blood_group\":\"\",\"registered_gp_name\":\"Melboure health centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-19 16:36:04"} 
[2025-07-22 10:44:30] local.ERROR: Failed to migrate user ID 884: Session store not set on request. {"ID":"884","user_login":"Philip_An93SK","user_email":"<EMAIL>","user_nicename":"philip_an93sk","display_name":"Philip Oldershaw","user_registered":"2025-06-04 09:24:30","first_name":"Philip","last_name":"Oldershaw","phone":null,"basic_data":"{\"mobile_number\":\"07834317447\",\"gender\":\"male\",\"dob\":\"1986-06-05\",\"nhs\":null,\"address\":\"1 Samian Close\",\"city\":\"Maldon\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 4PQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Longfield Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-04 10:24:30"} 
[2025-07-22 10:44:30] local.ERROR: Failed to migrate user ID 567: Session store not set on request. {"ID":"567","user_login":"Philip_d3hgyG","user_email":"<EMAIL>","user_nicename":"philip_d3hgyg","display_name":"Philip Wheatley","user_registered":"2025-04-02 13:55:25","first_name":"Philip","last_name":"Wheatley","phone":null,"basic_data":"{\"mobile_number\":\"07527042030\",\"gender\":\"male\",\"dob\":\"1972-08-11\",\"nhs\":null,\"address\":\"3 Chestnut Place\",\"city\":\"Essex\",\"state\":\"\",\"country\":null,\"postal_code\":\"CO10 7BT\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-02 14:55:25"} 
[2025-07-22 10:44:30] local.ERROR: Failed to migrate user ID 694: Session store not set on request. {"ID":"694","user_login":"Philippa_Ddrwxf","user_email":"<EMAIL>","user_nicename":"philippa_ddrwxf","display_name":"Philippa Gage","user_registered":"2025-04-23 17:20:21","first_name":"Philippa","last_name":"Gage","phone":null,"basic_data":"{\"mobile_number\":\"07774568425\",\"gender\":\"female\",\"dob\":\"1980-03-03\",\"nhs\":null,\"address\":\"18 Bulford Close Tye Green Cressing Braintree Essex CM77 8HH\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM778HH\",\"blood_group\":\"\",\"registered_gp_name\":\"Silver End Surgery\",\"registered_gp_address\":\"Silver End Surgery 3 Broadway Silver End Witham CM8 3RQ\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-24 11:59:58"} 
[2025-07-22 10:44:30] local.ERROR: Failed to migrate user ID 422: Session store not set on request. {"ID":"422","user_login":"Phyllis_hI72sN","user_email":"<EMAIL>","user_nicename":"phyllis_hi72sn","display_name":"Phyllis Opoku-Gyimah","user_registered":"2025-03-09 10:50:40","first_name":"Phyllis","last_name":"Opoku-Gyimah","phone":null,"basic_data":"{\"mobile_number\":\"07846365525\",\"gender\":\"female\",\"dob\":\"1974-11-20\",\"nhs\":null,\"address\":\"40 Monkhams Ave\",\"city\":\"Redbridge\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"IG8 0EY\",\"blood_group\":\"\",\"registered_gp_name\":\"Hensworth Medical Practice\",\"registered_gp_address\":\"E4\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-09 10:50:40"} 
[2025-07-22 10:44:31] local.ERROR: Failed to migrate user ID 619: Session store not set on request. {"ID":"619","user_login":"qqq_dzmeEg","user_email":"<EMAIL>","user_nicename":"qqq_dzmeeg","display_name":"qqq qqq","user_registered":"2025-04-12 09:02:05","first_name":"qqq","last_name":"qqq","phone":null,"basic_data":"{\"mobile_number\":\"1111111\",\"dob\":\"1111-11-11\",\"nhs\":\"\",\"address\":\"1111\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"1111\",\"registered_gp_address\":\"1111\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 10:02:05"} 
[2025-07-22 10:44:31] local.ERROR: Failed to migrate user ID 378: Session store not set on request. {"ID":"378","user_login":"raja_nJrtKD","user_email":"<EMAIL>","user_nicename":"raja_njrtkd","display_name":"raja mo","user_registered":"2025-03-02 13:26:52","first_name":"raja","last_name":"mo","phone":null,"basic_data":"{\"mobile_number\":\"075356373\",\"dob\":\"2025-02-06\",\"nhs\":\"\",\"address\":\"d\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"f\",\"registered_gp_address\":\"f\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-02 13:26:52"} 
[2025-07-22 10:44:31] local.ERROR: Failed to migrate user ID 229: Session store not set on request. {"ID":"229","user_login":"Raja_Vd21gC","user_email":"<EMAIL>","user_nicename":"raja_vd21gc","display_name":"Raja Mohan","user_registered":"2025-01-29 13:07:19","first_name":"Raja","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1222-11-11\",\"nhs\":\"\",\"address\":\"25 LINGE AVENUE\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM16BY\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-29 13:07:19"} 
[2025-07-22 10:44:31] local.ERROR: Failed to migrate user ID 375: Session store not set on request. {"ID":"375","user_login":"Raja_Lkry20","user_email":"<EMAIL>","user_nicename":"raja_lkry20","display_name":"Raja Mohan","user_registered":"2025-03-01 13:53:06","first_name":"Raja","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1111-11-11\",\"nhs\":\"\",\"address\":\"25 LINGE AVENUE SPRINGFIELD\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"2233\",\"registered_gp_address\":\"25 LINGE AVENUE\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-01 13:53:06"} 
[2025-07-22 10:44:32] local.ERROR: Failed to migrate user ID 382: Session store not set on request. {"ID":"382","user_login":"Raja_sX6hU0","user_email":"<EMAIL>","user_nicename":"raja_sx6hu0","display_name":"Raja Mohan","user_registered":"2025-03-03 07:07:32","first_name":"Raja","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-03-04\",\"nhs\":\"\",\"address\":\"25 Linge Avenue Springfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Rivermead\",\"registered_gp_address\":\"25 Linge Avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-03 07:07:32"} 
[2025-07-22 10:44:32] local.ERROR: Failed to migrate user ID 579: Session store not set on request. {"ID":"579","user_login":"Raja_0NSo8l","user_email":"<EMAIL>","user_nicename":"raja_0nso8l","display_name":"Raja Mohan","user_registered":"2025-04-06 09:10:38","first_name":"Raja","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-01\",\"nhs\":\"\",\"address\":\"25 Linge Avenue Springfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Hdhdh\",\"registered_gp_address\":\"25 Linge Avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 10:10:38"} 
[2025-07-22 10:44:32] local.ERROR: Failed to migrate user ID 638: Session store not set on request. {"ID":"638","user_login":"Raja_1e9MlK","user_email":"<EMAIL>","user_nicename":"raja_1e9mlk","display_name":"Raja Mohan","user_registered":"2025-04-14 17:42:28","first_name":"Raja","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-04-18\",\"nhs\":\"\",\"address\":\"25 Linge Avenue Springfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Hi\",\"registered_gp_address\":\"25 Linge Avenue\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-14 18:42:28"} 
[2025-07-22 10:44:32] local.ERROR: Failed to migrate user ID 339: Session store not set on request. {"ID":"339","user_login":"Raja_Rajan_EeBCVU","user_email":"<EMAIL>","user_nicename":"raja_rajan_eebcvu","display_name":"Raja Rajan Chandra Mohan","user_registered":"2025-02-23 11:24:20","first_name":"Raja Rajan","last_name":"Chandra Mohan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-02-20\",\"nhs\":\"\",\"address\":\"25 Linge Avenue Springfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM1 6BY\",\"gender\":\"male\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-23 11:24:20"} 
[2025-07-22 10:44:33] local.ERROR: Failed to migrate user ID 642: Session store not set on request. {"ID":"642","user_login":"Ranee_8PIBjM","user_email":"<EMAIL>","user_nicename":"ranee_8pibjm","display_name":"Ranee Edwards","user_registered":"2025-04-15 16:14:21","first_name":"Ranee","last_name":"Edwards","phone":null,"basic_data":"{\"mobile_number\":\"07702001711\",\"gender\":\"female\",\"dob\":\"1954-01-06\",\"nhs\":null,\"address\":\"12 Victoria Street Felixstowe\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"IP117EW\",\"blood_group\":\"\",\"registered_gp_name\":\"Grove Clinic Felixstowe\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-15 17:14:21"} 
[2025-07-22 10:44:33] local.ERROR: Failed to migrate user ID 662: Session store not set on request. {"ID":"662","user_login":"Raymond_U4DRVA","user_email":"<EMAIL>","user_nicename":"raymond_u4drva","display_name":"Raymond Bennett","user_registered":"2025-04-19 13:43:14","first_name":"Raymond","last_name":"Bennett","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2020-05-04\",\"nhs\":\"\",\"address\":\"21 orchard road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Longfield medical centre\",\"registered_gp_address\":\"21 orchard road\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-19 14:43:14"} 
[2025-07-22 10:44:33] local.ERROR: Failed to migrate user ID 493: Session store not set on request. {"ID":"493","user_login":"Reeta_KSPfqL","user_email":"<EMAIL>","user_nicename":"reeta_kspfql","display_name":"Reeta Basu","user_registered":"2025-03-21 16:30:22","first_name":"Reeta","last_name":"Basu","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1953-11-01\",\"nhs\":\"\",\"address\":\"5 Grove End E18 2LE\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Sarah Hayes\",\"registered_gp_address\":\"THE SHRUBBERIES E18 1BD\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-21 16:30:22"} 
[2025-07-22 10:44:33] local.ERROR: Failed to migrate user ID 814: Session store not set on request. {"ID":"814","user_login":"Reginald_4ROYGI","user_email":"<EMAIL>","user_nicename":"reginald_4roygi","display_name":"Reginald Golding","user_registered":"2025-05-20 13:32:21","first_name":"Reginald","last_name":"Golding","phone":null,"basic_data":"{\"mobile_number\":\"07990800964\",\"gender\":\"male\",\"dob\":\"1947-07-07\",\"nhs\":null,\"address\":\"20 Plantation Road\",\"city\":\"Boreham\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 3EA\",\"blood_group\":\"\",\"registered_gp_name\":\"The Laurals\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-20 14:32:21"} 
[2025-07-22 10:44:34] local.ERROR: Failed to migrate user ID 847: Session store not set on request. {"ID":"847","user_login":"Reuben_FvzOx9","user_email":"<EMAIL>","user_nicename":"reuben_fvzox9","display_name":"Reuben Morris","user_registered":"2025-05-28 09:37:26","first_name":"Reuben","last_name":"Morris","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2010-02-18\",\"nhs\":\"************\",\"address\":\"2 Gibson Vale Broomfield\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Mackin\",\"registered_gp_address\":\"Little Waltham surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-28 10:37:26"} 
[2025-07-22 10:44:34] local.ERROR: Failed to migrate user ID 426: Session store not set on request. {"ID":"426","user_login":"Rhina_stn51b","user_email":"<EMAIL>","user_nicename":"rhina_stn51b","display_name":"Rhina Lafnoune","user_registered":"2025-03-10 00:35:21","first_name":"Rhina","last_name":"Lafnoune","phone":null,"basic_data":"{\"mobile_number\":\"07979949598\",\"dob\":\"2001-02-20\",\"nhs\":\"\",\"address\":\"12 Banham Drive CM1 6HA\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Abingdon Medical Practice\",\"registered_gp_address\":\"W8 6EG\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-10 00:35:21"} 
[2025-07-22 10:44:34] local.ERROR: Failed to migrate user ID 760: Session store not set on request. {"ID":"760","user_login":"Riccardo_lQZ1vK","user_email":"<EMAIL>","user_nicename":"riccardo_lqz1vk","display_name":"Riccardo Tazzini","user_registered":"2025-05-09 15:45:29","first_name":"Riccardo","last_name":"Tazzini","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1967-06-09\",\"nhs\":\"**********\",\"address\":\"19 Sunningdale Road\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Melbourne surgery\",\"registered_gp_address\":\"19 Sunningdale Road\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-09 16:45:29"} 
[2025-07-22 10:44:34] local.ERROR: Failed to migrate user ID 769: Session store not set on request. {"ID":"769","user_login":"Richard_AsZ6mH","user_email":"<EMAIL>","user_nicename":"richard_asz6mh","display_name":"Richard Cousins","user_registered":"2025-05-11 09:29:24","first_name":"Richard","last_name":"Cousins","phone":null,"basic_data":"{\"mobile_number\":\"07946505561\",\"gender\":\"male\",\"dob\":\"1980-11-17\",\"nhs\":null,\"address\":\"9 Dukes Lane\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 6AD\",\"blood_group\":\"\",\"registered_gp_name\":\"Sutherland Lodge\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-11 10:29:24"} 
[2025-07-22 10:44:35] local.ERROR: Failed to migrate user ID 591: Session store not set on request. {"ID":"591","user_login":"Richard_buA7iW","user_email":"<EMAIL>","user_nicename":"richard_bua7iw","display_name":"Richard Keogh","user_registered":"2025-04-07 15:33:57","first_name":"Richard","last_name":"Keogh","phone":null,"basic_data":"{\"mobile_number\":\"07881542769\",\"dob\":\"1969-06-03\",\"nhs\":\"\",\"address\":\"10 Cedar Road NW2 6SR\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Skelpa\",\"registered_gp_address\":\"The Windmill Practice, 65 Shoot up Hill NW2 3PS\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 16:33:57"} 
[2025-07-22 10:44:35] local.ERROR: Failed to migrate user ID 878: Session store not set on request. {"ID":"878","user_login":"Richard_J0Xb1Z","user_email":"<EMAIL>","user_nicename":"richard_j0xb1z","display_name":"Richard Weatherstone","user_registered":"2025-06-03 09:09:01","first_name":"Richard","last_name":"Weatherstone","phone":null,"basic_data":"{\"mobile_number\":\"07761505213\",\"gender\":\"male\",\"dob\":\"1954-06-21\",\"nhs\":null,\"address\":\"1 kilnfield Barn, Chignal road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4GR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-03 10:09:01"} 
[2025-07-22 10:44:35] local.ERROR: Failed to migrate user ID 474: Session store not set on request. {"ID":"474","user_login":"Rita_nq9VTH","user_email":"<EMAIL>","user_nicename":"rita_nq9vth","display_name":"Rita Bowkett-Smith","user_registered":"2025-03-18 10:10:47","first_name":"Rita","last_name":"Bowkett-Smith","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1962-01-23\",\"nhs\":\"************\",\"address\":\"The Old Stables Rysley, Little Baddow\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"danbury medical centre\",\"registered_gp_address\":\"The Old Stables\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-18 10:10:47"} 
[2025-07-22 10:44:35] local.ERROR: Failed to migrate user ID 698: Session store not set on request. {"ID":"698","user_login":"Robert_vhVFjG","user_email":"<EMAIL>","user_nicename":"robert_vhvfjg","display_name":"Robert Barlow","user_registered":"2025-04-24 13:01:28","first_name":"Robert","last_name":"Barlow","phone":null,"basic_data":"{\"mobile_number\":\"01248604270\",\"gender\":\"male\",\"dob\":\"1933-04-26\",\"nhs\":null,\"address\":\"7 Pennine Road Chelmsford Wife's mobile no: patient to provide\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM12HG\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-24 14:38:35"} 
[2025-07-22 10:44:36] local.ERROR: Failed to migrate user ID 889: Session store not set on request. {"ID":"889","user_login":"Robert_XlzMRu","user_email":"<EMAIL>","user_nicename":"robert_xlzmru","display_name":"Robert Coe","user_registered":"2025-06-06 10:31:09","first_name":"Robert","last_name":"Coe","phone":null,"basic_data":"{\"mobile_number\":\"07984616644\",\"dob\":\"1974-06-26\",\"nhs\":\"**********\",\"address\":\"28 Maple Way Dunmow\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr T Brasse\",\"registered_gp_address\":\"John Tasker House, 58 New Street, Dunmow, Essex, CM6 1BH\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-06 11:31:09"} 
[2025-07-22 10:44:36] local.ERROR: Failed to migrate user ID 633: Session store not set on request. {"ID":"633","user_login":"Robert_Jqy1CA","user_email":"<EMAIL>","user_nicename":"robert_jqy1ca","display_name":"Robert Finnerty","user_registered":"2025-04-13 14:46:55","first_name":"Robert","last_name":"Finnerty","phone":null,"basic_data":"{\"mobile_number\":\"07860338932\",\"gender\":\"male\",\"dob\":\"1991-10-31\",\"nhs\":null,\"address\":\"Rutlands, Evelyn Road\",\"city\":\"Felsted\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1QQ\",\"blood_group\":\"\",\"registered_gp_name\":\"John Tasker House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 15:46:55"} 
[2025-07-22 10:44:36] local.ERROR: Failed to migrate user ID 846: Session store not set on request. {"ID":"846","user_login":"Robert_qrdUSF","user_email":"<EMAIL>","user_nicename":"robert_qrdusf","display_name":"Robert Giles","user_registered":"2025-05-27 10:52:46","first_name":"Robert","last_name":"Giles","phone":null,"basic_data":"{\"mobile_number\":\"07768975977\",\"gender\":\"male\",\"dob\":\"1938-07-22\",\"nhs\":null,\"address\":\"49 Heronway\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 2LQ\",\"blood_group\":\"\",\"registered_gp_name\":\"Brentwood\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-27 11:52:46"} 
[2025-07-22 10:44:36] local.ERROR: Failed to migrate user ID 748: Session store not set on request. {"ID":"748","user_login":"Robin_iNsZTr","user_email":"<EMAIL>","user_nicename":"robin_insztr","display_name":"Robin Taylor","user_registered":"2025-05-06 14:02:38","first_name":"Robin","last_name":"Taylor","phone":null,"basic_data":"{\"mobile_number\":\"07419300538\",\"gender\":\"male\",\"dob\":\"1955-11-19\",\"nhs\":null,\"address\":\"53 Nash Drive,\",\"city\":\"Broomfield\",\"state\":\"\",\"country\":null,\"postal_code\":\"cm1\",\"blood_group\":\"\",\"registered_gp_name\":\"Lt Loaltham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-06 15:02:38"} 
[2025-07-22 10:44:37] local.ERROR: Failed to migrate user ID 291: Session store not set on request. {"ID":"291","user_login":"Roger_KVvZiJ","user_email":"<EMAIL>","user_nicename":"roger_kvvzij","display_name":"Roger Gibbard","user_registered":"2025-02-11 14:45:39","first_name":"Roger","last_name":"Gibbard","phone":null,"basic_data":"{\"mobile_number\":\"07951917656\",\"gender\":\"male\",\"dob\":\"1937-03-14\",\"nhs\":null,\"address\":\"1 quinion close\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"cm14uh\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-11 14:47:28"} 
[2025-07-22 10:44:37] local.ERROR: Failed to migrate user ID 300: Session store not set on request. {"ID":"300","user_login":"Roger_ZU4S30","user_email":"<EMAIL>","user_nicename":"roger_zu4s30","display_name":"Roger Mitham","user_registered":"2025-02-12 19:17:38","first_name":"Roger","last_name":"Mitham","phone":null,"basic_data":"{\"mobile_number\":\"07761068623\",\"gender\":\"male\",\"dob\":\"1970-11-06\",\"nhs\":null,\"address\":\"110 Linnet Drive\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM2 8AG\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-12 19:17:38"} 
[2025-07-22 10:44:37] local.ERROR: Failed to migrate user ID 920: Session store not set on request. {"ID":"920","user_login":"Ron_k8gpaN","user_email":"<EMAIL>","user_nicename":"ron_k8gpan","display_name":"Ron Johnston","user_registered":"2025-06-12 10:08:49","first_name":"Ron","last_name":"Johnston","phone":null,"basic_data":"{\"mobile_number\":\"07947143983\",\"gender\":\"male\",\"dob\":\"1958-01-15\",\"nhs\":null,\"address\":\"Leylandii Cedar Avenue West Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2XA\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermade gate medical centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-12 11:08:49"} 
[2025-07-22 10:44:37] local.ERROR: Failed to migrate user ID 442: Session store not set on request. {"ID":"442","user_login":"Ronnie_AiN9zd","user_email":"<EMAIL>","user_nicename":"ronnie_ain9zd","display_name":"Ronnie Moore","user_registered":"2025-03-12 16:27:15","first_name":"Ronnie","last_name":"Moore","phone":null,"basic_data":"{\"mobile_number\":\"07596069030\",\"gender\":\"male\",\"dob\":\"2016-09-19\",\"nhs\":null,\"address\":\"15 Albert Road CM08EA\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM08EA\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-12 16:27:15"} 
[2025-07-22 10:44:38] local.ERROR: Failed to migrate user ID 467: Session store not set on request. {"ID":"467","user_login":"Rosemary_E0TwgI","user_email":"<EMAIL>","user_nicename":"rosemary_e0twgi","display_name":"Rosemary Martin","user_registered":"2025-03-16 18:52:04","first_name":"Rosemary","last_name":"Martin","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1954-04-13\",\"nhs\":\"\",\"address\":\"17 The Close Dunmow CM6 1EW\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"John Tasker House\",\"registered_gp_address\":\"56 New street, Dunmow, CM6 1BH\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-16 18:52:04"} 
[2025-07-22 10:44:38] local.ERROR: Failed to migrate user ID 756: Session store not set on request. {"ID":"756","user_login":"Rosie_ZR5sM0","user_email":"<EMAIL>","user_nicename":"rosie_zr5sm0","display_name":"Rosie Franks","user_registered":"2025-05-08 18:04:54","first_name":"Rosie","last_name":"Franks","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"2004-12-26\",\"nhs\":null,\"address\":\"Coleman House, Norwhich University\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"NR4 7TJ\",\"blood_group\":\"\",\"registered_gp_name\":\"UAE Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-08 19:04:54"} 
[2025-07-22 10:44:38] local.ERROR: Failed to migrate user ID 511: Session store not set on request. {"ID":"511","user_login":"Russell_8LgYbU","user_email":"<EMAIL>","user_nicename":"russell_8lgybu","display_name":"Russell Probert","user_registered":"2025-03-24 15:50:43","first_name":"Russell","last_name":"Probert","phone":null,"basic_data":"{\"mobile_number\":\"07854478288\",\"dob\":\"1993-06-05\",\"nhs\":\"\",\"address\":\"23 acres end Chelmford\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Chelmer Medical Partnership\",\"registered_gp_address\":\"Na\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-24 15:50:43"} 
[2025-07-22 10:44:38] local.ERROR: Failed to migrate user ID 1017: Session store not set on request. {"ID":"1017","user_login":"Ruth_GnldEz","user_email":"<EMAIL>","user_nicename":"ruth_gnldez","display_name":"Ruth Flanders","user_registered":"2025-07-02 12:01:31","first_name":"Ruth","last_name":"Flanders","phone":null,"basic_data":"{\"mobile_number\":\"07814375010\",\"gender\":\"female\",\"dob\":\"1992-07-19\",\"nhs\":null,\"address\":\"57 Baynard Avenue, Flitch Green\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 3FF\",\"blood_group\":\"\",\"registered_gp_name\":\"Angel Lane Surgery\",\"registered_gp_address\":\"Great Dunmow\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-02 13:01:32"} 
[2025-07-22 10:44:39] local.ERROR: Failed to migrate user ID 620: Session store not set on request. {"ID":"620","user_login":"Ryan_0ofV5P","user_email":"<EMAIL>","user_nicename":"ryan_0ofv5p","display_name":"Ryan Humberstone","user_registered":"2025-04-12 09:39:04","first_name":"Ryan","last_name":"Humberstone","phone":null,"basic_data":"{\"mobile_number\":\"07392307454\",\"gender\":\"male\",\"dob\":\"1988-12-27\",\"nhs\":null,\"address\":\"64 Baddow Hall Crescent\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 7BX\",\"blood_group\":\"\",\"registered_gp_name\":\"Baddow Village Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 10:39:04"} 
[2025-07-22 10:44:39] local.ERROR: Failed to migrate user ID 673: Session store not set on request. {"ID":"673","user_login":"sadasd_vc0x1e","user_email":"<EMAIL>","user_nicename":"sadasd_vc0x1e","display_name":"sadasd asdasdasd","user_registered":"2025-04-22 13:06:22","first_name":"sadasd","last_name":"asdasdasd","phone":null,"basic_data":"{\"mobile_number\":\"54654645654\",\"dob\":\"2025-04-07\",\"nhs\":\"987979\",\"address\":\"sadasdad\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"sadasdasd\",\"registered_gp_address\":\"sadasdasd\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 14:06:22"} 
[2025-07-22 10:44:39] local.ERROR: Failed to migrate user ID 407: Session store not set on request. {"ID":"407","user_login":"Sam_Z4ucsq","user_email":"<EMAIL>","user_nicename":"sam_z4ucsq","display_name":"Sam Woodcraft","user_registered":"2025-03-05 17:53:41","first_name":"Sam","last_name":"Woodcraft","phone":null,"basic_data":"{\"mobile_number\":\"07887682747\",\"gender\":\"male\",\"dob\":\"2009-05-10\",\"nhs\":\"**********\",\"address\":\"25 Villiers Place\",\"city\":\"Boreham\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM3 3JW\",\"blood_group\":\"\",\"registered_gp_name\":\"Sidney House And The Laurels\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-05 17:53:41"} 
[2025-07-22 10:44:39] local.ERROR: Failed to migrate user ID 303: Session store not set on request. {"ID":"303","user_login":"Samantha_wicsxh","user_email":"<EMAIL>","user_nicename":"samantha_wicsxh","display_name":"Samantha Berry","user_registered":"2025-02-13 12:39:04","first_name":"Samantha","last_name":"Berry","phone":null,"basic_data":"{\"mobile_number\":\"07876806126\",\"gender\":\"female\",\"dob\":\"1992-05-28\",\"nhs\":\"Park side medical centre\",\"address\":\"5, Whitehead Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM1 3GJ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-26 12:03:13"} 
[2025-07-22 10:44:40] local.ERROR: Failed to migrate user ID 341: Session store not set on request. {"ID":"341","user_login":"Samantha_lDsVHi","user_email":"<EMAIL>","user_nicename":"samantha_ldsvhi","display_name":"Samantha Padbury","user_registered":"2025-02-23 13:28:54","first_name":"Samantha","last_name":"Padbury","phone":null,"basic_data":"{\"mobile_number\":\"07368484646\",\"gender\":\"female\",\"dob\":\"1977-11-12\",\"nhs\":null,\"address\":\"10 Swallowtail glade, Stanway, CO3 0AH\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-23 13:28:54"} 
[2025-07-22 10:44:40] local.ERROR: Failed to migrate user ID 668: Session store not set on request. {"ID":"668","user_login":"Samantha_txVge8","user_email":"<EMAIL>","user_nicename":"samantha_txvge8","display_name":"Samantha Toovey","user_registered":"2025-04-21 16:12:52","first_name":"Samantha","last_name":"Toovey","phone":null,"basic_data":"{\"mobile_number\":\"07581089809\",\"dob\":\"1990-04-26\",\"nhs\":\"\",\"address\":\"13 wavell close Chelmsford Cm1 6fq\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Prasad\",\"registered_gp_address\":\"North Chelmsford health care centre\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-21 17:12:52"} 
[2025-07-22 10:44:40] local.ERROR: Failed to migrate user ID 1007: Session store not set on request. {"ID":"1007","user_login":"Samuel_lWxD0Y","user_email":"<EMAIL>","user_nicename":"samuel_lwxd0y","display_name":"Samuel Davis","user_registered":"2025-06-29 10:56:26","first_name":"Samuel","last_name":"Davis","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1999-09-30\",\"nhs\":\"\",\"address\":\"Flat 1 Cumberland court, Oaks drive, Colchester CO33PR\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-29 11:56:26"} 
[2025-07-22 10:44:40] local.ERROR: Failed to migrate user ID 667: Session store not set on request. {"ID":"667","user_login":"Samuel_awZbvq","user_email":"<EMAIL>","user_nicename":"samuel_awzbvq","display_name":"Samuel Padbury-Clark","user_registered":"2025-04-21 14:07:57","first_name":"Samuel","last_name":"Padbury-Clark","phone":null,"basic_data":"{\"mobile_number\":\"07899668060\",\"gender\":\"male\",\"dob\":\"1980-03-01\",\"nhs\":null,\"address\":\"The old workshop\",\"city\":\"Chelmford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1DZ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-21 15:07:57"} 
[2025-07-22 10:44:41] local.ERROR: Failed to migrate user ID 692: Session store not set on request. {"ID":"692","user_login":"Sandra_gthLDX","user_email":"<EMAIL>","user_nicename":"sandra_gthldx","display_name":"Sandra O'Hare","user_registered":"2025-04-23 14:16:15","first_name":"Sandra","last_name":"O'Hare","phone":null,"basic_data":"{\"mobile_number\":\"07941381666\",\"gender\":\"female\",\"dob\":\"1943-10-27\",\"nhs\":null,\"address\":\"25 Harewood Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3DQ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-28 12:21:17"} 
[2025-07-22 10:44:41] local.ERROR: Failed to migrate user ID 851: Session store not set on request. {"ID":"851","user_login":"Sandra_iP5Jw2","user_email":"<EMAIL>","user_nicename":"sandra_ip5jw2","display_name":"Sandra Sebi","user_registered":"2025-05-28 16:50:16","first_name":"Sandra","last_name":"Sebi","phone":null,"basic_data":"{\"mobile_number\":\"07733680870\",\"gender\":\"female\",\"dob\":\"2004-12-01\",\"nhs\":null,\"address\":\"43 Kings Road, Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4HR\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermade Gate med centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-28 17:56:10"} 
[2025-07-22 10:44:41] local.ERROR: Failed to migrate user ID 799: Session store not set on request. {"ID":"799","user_login":"Saoud_ycJkUo","user_email":"<EMAIL>","user_nicename":"saoud_ycjkuo","display_name":"Saoud Sultan","user_registered":"2025-05-18 08:44:58","first_name":"Saoud","last_name":"Sultan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1965-02-01\",\"nhs\":null,\"address\":\"27 Cornelius vale CM2 6GY\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"North Chelmsford NHS Centre\",\"registered_gp_address\":\"2 White Hart lane\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 10:12:49"} 
[2025-07-22 10:44:41] local.ERROR: Failed to migrate user ID 743: Session store not set on request. {"ID":"743","user_login":"Sara_3asIDE","user_email":"<EMAIL>","user_nicename":"sara_3aside","display_name":"Sara Sayer","user_registered":"2025-05-04 12:28:14","first_name":"Sara","last_name":"Sayer","phone":null,"basic_data":"{\"mobile_number\":\"07891107966\",\"gender\":\"female\",\"dob\":\"1978-03-17\",\"nhs\":null,\"address\":\"83 Park Ave\",\"city\":\"Brentwood\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 2QL\",\"blood_group\":\"\",\"registered_gp_name\":\"Mount Ave Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-04 13:28:14"} 
[2025-07-22 10:44:42] local.ERROR: Failed to migrate user ID 830: Session store not set on request. {"ID":"830","user_login":"Sarah_9xUNyA","user_email":"<EMAIL>","user_nicename":"sarah_9xunya","display_name":"Sarah Barker-Nunn","user_registered":"2025-05-24 10:27:46","first_name":"Sarah","last_name":"Barker-Nunn","phone":null,"basic_data":"{\"mobile_number\":\"07766023171\",\"gender\":\"female\",\"dob\":\"1991-08-10\",\"nhs\":null,\"address\":\"331, Broomfield Rd, Chelmsford, CM1 4DU\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4DU\",\"blood_group\":\"\",\"registered_gp_name\":\"Dr Ahmed\",\"registered_gp_address\":\"tenyson house\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-09 20:48:25"} 
[2025-07-22 10:44:42] local.ERROR: Failed to migrate user ID 336: Session store not set on request. {"ID":"336","user_login":"Sarah_Lbf9jJ","user_email":"<EMAIL>","user_nicename":"sarah_lbf9jj","display_name":"Sarah Clarke","user_registered":"2025-02-22 13:29:19","first_name":"Sarah","last_name":"Clarke","phone":null,"basic_data":"{\"mobile_number\":\"07704923685\",\"gender\":\"female\",\"dob\":\"1989-01-05\",\"nhs\":null,\"address\":\"25 homefield road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SW19 4QF\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-22 13:29:19"} 
[2025-07-22 10:44:42] local.ERROR: Failed to migrate user ID 497: Session store not set on request. {"ID":"497","user_login":"Sarah_M3uD6G","user_email":"<EMAIL>","user_nicename":"sarah_m3ud6g","display_name":"Sarah Davies","user_registered":"2025-03-22 15:01:33","first_name":"Sarah","last_name":"Davies","phone":null,"basic_data":"{\"mobile_number\":\"07776182761\",\"dob\":\"1979-07-23\",\"nhs\":\"\",\"address\":\"12 mildmays Danbury cm34dp\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Wyncroft surgery bicknacre\",\"registered_gp_address\":\"Wyncroft bicknacre\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 15:01:33"} 
[2025-07-22 10:44:42] local.ERROR: Failed to migrate user ID 389: Session store not set on request. {"ID":"389","user_login":"Sarah_8BPxKl","user_email":"<EMAIL>","user_nicename":"sarah_8bpxkl","display_name":"Sarah Maggs","user_registered":"2025-03-03 18:48:22","first_name":"Sarah","last_name":"Maggs","phone":null,"basic_data":"{\"mobile_number\":\"07982493169\",\"gender\":\"female\",\"dob\":\"2000-11-20\",\"nhs\":null,\"address\":\"66 Beehive Lane, CM29RX\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM29RX\",\"blood_group\":\"\",\"registered_gp_name\":\"Moulsham Lodge Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-03 18:48:22"} 
[2025-07-22 10:44:43] local.ERROR: Failed to migrate user ID 829: Session store not set on request. {"ID":"829","user_login":"Sarah_May_Georgia_rV2TiU","user_email":"<EMAIL>","user_nicename":"sarah_may_georgia_rv2tiu","display_name":"Sarah May Georgia Ryland","user_registered":"2025-05-24 10:09:32","first_name":"Sarah May Georgia","last_name":"Ryland","phone":null,"basic_data":"{\"mobile_number\":\"07903603465\",\"gender\":\"female\",\"dob\":\"1986-07-20\",\"nhs\":\"**********\",\"address\":\"3 Gershwin Boulevard\",\"city\":\"Witham\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM8 1HW\",\"blood_group\":\"\",\"registered_gp_name\":\"Fern House, Witham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-24 11:09:32"} 
[2025-07-22 10:44:43] local.ERROR: Failed to migrate user ID 451: Session store not set on request. {"ID":"451","user_login":"Sarah_sdFgeS","user_email":"<EMAIL>","user_nicename":"sarah_sdfges","display_name":"Sarah Walsh","user_registered":"2025-03-14 15:59:06","first_name":"Sarah","last_name":"Walsh","phone":null,"basic_data":"{\"mobile_number\":\"07717858078\",\"gender\":\"female\",\"dob\":\"1973-05-28\",\"nhs\":\"**********\",\"address\":\"1 The Green Chignal St James Chelmsford CM1 4TY\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Dr Savici\",\"registered_gp_address\":\"The Writtle Surgery, Lordship Road, Writtle, Chelmsford\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-25 10:19:55"} 
[2025-07-22 10:44:43] local.ERROR: Failed to migrate user ID 979: Session store not set on request. {"ID":"979","user_login":"Scarlett_tfdYU8","user_email":"<EMAIL>","user_nicename":"scarlett_tfdyu8","display_name":"Scarlett O'Malley","user_registered":"2025-06-21 14:30:32","first_name":"Scarlett","last_name":"O'Malley","phone":null,"basic_data":"{\"mobile_number\":\"01245443223\",\"gender\":\"female\",\"dob\":\"2005-09-19\",\"nhs\":null,\"address\":\"12 Emberson Croft\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4FD\",\"blood_group\":\"\",\"registered_gp_name\":\"Kripps Health, Nottingham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-21 15:30:32"} 
[2025-07-22 10:44:43] local.ERROR: Failed to migrate user ID 747: Session store not set on request. {"ID":"747","user_login":"Scott_SjZNEJ","user_email":"<EMAIL>","user_nicename":"scott_sjznej","display_name":"Scott Christmas","user_registered":"2025-05-06 13:56:25","first_name":"Scott","last_name":"Christmas","phone":null,"basic_data":"{\"mobile_number\":\"07909354024\",\"gender\":\"male\",\"dob\":\"1992-04-06\",\"nhs\":null,\"address\":\"42 Chestnut Avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"I996EW\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-06 14:56:25"} 
[2025-07-22 10:44:44] local.ERROR: Failed to migrate user ID 831: Session store not set on request. {"ID":"831","user_login":"Sean_YUjaXT","user_email":"<EMAIL>","user_nicename":"sean_yujaxt","display_name":"Sean Dunphy","user_registered":"2025-05-24 10:50:56","first_name":"Sean","last_name":"Dunphy","phone":null,"basic_data":"{\"mobile_number\":\"07957785615\",\"dob\":\"1972-08-10\",\"nhs\":\"\",\"address\":\"3 mill road Henham CM22 6AD\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Elsenham Surgeries\",\"registered_gp_address\":\"Station Road, elsenham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-24 11:50:56"} 
[2025-07-22 10:44:44] local.ERROR: Failed to migrate user ID 664: Session store not set on request. {"ID":"664","user_login":"Sean_D6J7zj","user_email":"<EMAIL>","user_nicename":"sean_d6j7zj","display_name":"Sean Nash","user_registered":"2025-04-20 08:56:42","first_name":"Sean","last_name":"Nash","phone":null,"basic_data":"{\"mobile_number\":\"07730319301\",\"dob\":\"1972-06-16\",\"nhs\":\"\",\"address\":\"3 Buttercup Close, Little Canfield, Dunmow, Essex CM6 4AJ\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"John Tasker Surgery\",\"registered_gp_address\":\"Dunmow Essex.\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-20 09:56:42"} 
[2025-07-22 10:44:44] local.ERROR: Failed to migrate user ID 730: Session store not set on request. {"ID":"730","user_login":"Sean_HB34nL","user_email":"<EMAIL>","user_nicename":"sean_hb34nl","display_name":"Sean Quinn","user_registered":"2025-04-30 14:11:08","first_name":"Sean","last_name":"Quinn","phone":null,"basic_data":"{\"mobile_number\":\"07905060611\",\"gender\":\"male\",\"dob\":\"1968-03-26\",\"nhs\":null,\"address\":\"51 Rutlang Road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM14BL\",\"blood_group\":\"\",\"registered_gp_name\":\"Tennyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-30 15:16:32"} 
[2025-07-22 10:44:44] local.ERROR: Failed to migrate user ID 956: Session store not set on request. {"ID":"956","user_login":"Shamine_YtRVPp","user_email":"<EMAIL>","user_nicename":"shamine_ytrvpp","display_name":"Shamine Ambrose","user_registered":"2025-06-17 22:56:38","first_name":"Shamine","last_name":"Ambrose","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1993-04-21\",\"nhs\":\"************\",\"address\":\"63 Parkinson drive Chelmsford CM1 3GU\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Reshma Rasheed\",\"registered_gp_address\":\"Corringham Health Centre 114 Giffords Cross Road, Corringham SS17 7QQ\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-17 23:56:38"} 
[2025-07-22 10:44:45] local.ERROR: Failed to migrate user ID 833: Session store not set on request. {"ID":"833","user_login":"Sharon_nkRYq9","user_email":"<EMAIL>","user_nicename":"sharon_nkryq9","display_name":"Sharon Honey","user_registered":"2025-05-24 13:42:37","first_name":"Sharon","last_name":"Honey","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1957-03-27\",\"nhs\":\"\",\"address\":\"Hill Farm The Street Woodham Ferrers CM3 8RG\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Susan Matthew\",\"registered_gp_address\":\"Kingsway Surgery, Crouch ValeMedical Centre Burnham Road Woodham Ferrers\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-24 14:42:37"} 
[2025-07-22 10:44:45] local.ERROR: Failed to migrate user ID 866: Session store not set on request. {"ID":"866","user_login":"shelley_tvpxZ6","user_email":"<EMAIL>","user_nicename":"shelley_tvpxz6","display_name":"shelley Newman","user_registered":"2025-06-01 09:01:03","first_name":"shelley","last_name":"Newman","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1987-04-09\",\"nhs\":\"\",\"address\":\"Brick kiln cottage Boyton cross lane Chelmsford CM1 4LD\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Rivermead gate surgery\",\"registered_gp_address\":\"Chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 10:01:03"} 
[2025-07-22 10:44:45] local.ERROR: Failed to migrate user ID 908: Session store not set on request. {"ID":"908","user_login":"Shruthi_trPzTO","user_email":"<EMAIL>","user_nicename":"shruthi_trpzto","display_name":"Shruthi Pathipati","user_registered":"2025-06-09 13:57:16","first_name":"Shruthi","last_name":"Pathipati","phone":null,"basic_data":"{\"mobile_number\":\"07586538414\",\"gender\":\"female\",\"dob\":\"1998-11-16\",\"nhs\":null,\"address\":\"Flat 602, Orchestra House, Ilford Hill\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"IG1 2ZS\",\"blood_group\":\"\",\"registered_gp_name\":\"Carpenters Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-09 14:57:16"} 
[2025-07-22 10:44:46] local.ERROR: Failed to migrate user ID 629: Session store not set on request. {"ID":"629","user_login":"Siri_Uc9gpB","user_email":"<EMAIL>","user_nicename":"siri_uc9gpb","display_name":"Siri Brandt","user_registered":"2025-04-13 12:23:56","first_name":"Siri","last_name":"Brandt","phone":null,"basic_data":"{\"mobile_number\":\"15141447450\",\"gender\":\"male\",\"dob\":\"2008-12-28\",\"nhs\":null,\"address\":\"Isaraa\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 3SH\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-13 13:23:56"} 
[2025-07-22 10:44:46] local.ERROR: Failed to migrate user ID 337: Session store not set on request. {"ID":"337","user_login":"Sofia_OYXGlE","user_email":"<EMAIL>","user_nicename":"sofia_oyxgle","display_name":"Sofia Treadwell","user_registered":"2025-02-23 08:34:41","first_name":"Sofia","last_name":"Treadwell","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"2021-10-05\",\"nhs\":null,\"address\":\"131 London Road Wickford Essex\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"Ss12 0au\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-23 08:48:25"} 
[2025-07-22 10:44:46] local.ERROR: Failed to migrate user ID 622: Session store not set on request. {"ID":"622","user_login":"Sonia_ARVz7k","user_email":"<EMAIL>","user_nicename":"sonia_arvz7k","display_name":"Sonia Kwok","user_registered":"2025-04-12 10:35:09","first_name":"Sonia","last_name":"Kwok","phone":null,"basic_data":"{\"mobile_number\":\"07843988231\",\"gender\":\"female\",\"dob\":\"1956-03-19\",\"nhs\":null,\"address\":\"26 Micawber Way\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4UG\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Medical Centre\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-12 11:35:09"} 
[2025-07-22 10:44:46] local.ERROR: Failed to migrate user ID 649: Session store not set on request. {"ID":"649","user_login":"Sophia_jZh8S4","user_email":"<EMAIL>","user_nicename":"sophia_jzh8s4","display_name":"Sophia Malik","user_registered":"2025-04-17 16:27:38","first_name":"Sophia","last_name":"Malik","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1979-02-10\",\"nhs\":\"\",\"address\":\"12 Reed Pond Walk Gidea Park Romford Essex RM25PB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Edison, The New Medical Centre\",\"registered_gp_address\":\"264 Brentwood Road, Romford, Essex\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-17 17:27:38"} 
[2025-07-22 10:44:47] local.ERROR: Failed to migrate user ID 893: Session store not set on request. {"ID":"893","user_login":"Sophie_qv8pPO","user_email":"<EMAIL>","user_nicename":"sophie_qv8ppo","display_name":"Sophie Whelan","user_registered":"2025-06-07 09:55:17","first_name":"Sophie","last_name":"Whelan","phone":null,"basic_data":"{\"mobile_number\":\"07906042859\",\"gender\":\"female\",\"dob\":\"2005-06-02\",\"nhs\":null,\"address\":\"19 The Close\",\"city\":\"Great Dunmow\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM6 1EW\",\"blood_group\":\"\",\"registered_gp_name\":\"Bristol University Health Service\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-07 10:55:17"} 
[2025-07-22 10:44:47] local.ERROR: Failed to migrate user ID 669: Session store not set on request. {"ID":"669","user_login":"Stephanie_4pBEeL","user_email":"<EMAIL>","user_nicename":"stephanie_4pbeel","display_name":"Stephanie Rodley","user_registered":"2025-04-22 07:55:52","first_name":"Stephanie","last_name":"Rodley","phone":null,"basic_data":"{\"mobile_number\":\"07860200499\",\"dob\":\"2003-12-21\",\"nhs\":\"**********\",\"address\":\"Roebuck House Pleshey Essex Cm3 1hy\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Wijkekoon\",\"registered_gp_address\":\"Little waltham surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-22 08:55:52"} 
[2025-07-22 10:44:47] local.ERROR: Failed to migrate user ID 983: Session store not set on request. {"ID":"983","user_login":"Steven_fBxOu5","user_email":"<EMAIL>","user_nicename":"steven_fbxou5","display_name":"Steven Brown","user_registered":"2025-06-23 10:59:50","first_name":"Steven","last_name":"Brown","phone":null,"basic_data":"{\"mobile_number\":\"07792383895\",\"gender\":\"male\",\"dob\":\"1992-04-05\",\"nhs\":null,\"address\":\"19 Clover Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"cm1 4ft\",\"blood_group\":\"\",\"registered_gp_name\":\"Chadwell Heath Surgery\",\"registered_gp_address\":\"RM6 4AF\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-23 11:59:50"} 
[2025-07-22 10:44:47] local.ERROR: Failed to migrate user ID 693: Session store not set on request. {"ID":"693","user_login":"Steven_7vTxlk","user_email":"<EMAIL>","user_nicename":"steven_7vtxlk","display_name":"Steven Buckley","user_registered":"2025-04-23 15:58:34","first_name":"Steven","last_name":"Buckley","phone":null,"basic_data":"{\"mobile_number\":\"07983770978\",\"gender\":\"male\",\"dob\":\"1974-09-02\",\"nhs\":null,\"address\":\"132 Aval Road\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM12CA\",\"blood_group\":\"\",\"registered_gp_name\":\"Tenneyson House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-23 18:14:36"} 
[2025-07-22 10:44:48] local.ERROR: Failed to migrate user ID 538: Session store not set on request. {"ID":"538","user_login":"Steven_5cYLWK","user_email":"<EMAIL>","user_nicename":"steven_5cylwk","display_name":"Steven Yarnall","user_registered":"2025-03-27 17:34:43","first_name":"Steven","last_name":"Yarnall","phone":null,"basic_data":"{\"mobile_number\":\"07838253534\",\"dob\":\"1967-06-21\",\"nhs\":\"\",\"address\":\"Bridge House, Farmbridge End Road, Good Easter. Chelmsford CM1 4RY\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Dr Kumar\",\"registered_gp_address\":\"Writtle Surgery\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-27 17:34:43"} 
[2025-07-22 10:44:48] local.ERROR: Failed to migrate user ID 443: Session store not set on request. {"ID":"443","user_login":"Surekha_rvsyaI","user_email":"<EMAIL>","user_nicename":"surekha_rvsyai","display_name":"Surekha Chitithoti","user_registered":"2025-03-12 17:02:32","first_name":"Surekha","last_name":"Chitithoti","phone":null,"basic_data":"{\"mobile_number\":\"07879791543\",\"gender\":\"female\",\"dob\":\"1978-12-11\",\"nhs\":null,\"address\":\"16 John eve avenue\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM16DF\",\"blood_group\":\"\",\"registered_gp_name\":\"NCHC\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-13 18:00:43"} 
[2025-07-22 10:44:48] local.ERROR: Failed to migrate user ID 985: Session store not set on request. {"ID":"985","user_login":"Susan_BJNqwi","user_email":"<EMAIL>","user_nicename":"susan_bjnqwi","display_name":"Susan Anderson","user_registered":"2025-06-23 12:49:05","first_name":"Susan","last_name":"Anderson","phone":null,"basic_data":"{\"mobile_number\":\"07730558257\",\"gender\":\"female\",\"dob\":\"1957-10-17\",\"nhs\":null,\"address\":\"24 Weller Grove\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4YJ\",\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Centre\",\"registered_gp_address\":\"<EMAIL>\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-23 13:49:05"} 
[2025-07-22 10:44:48] local.ERROR: Failed to migrate user ID 958: Session store not set on request. {"ID":"958","user_login":"Susan_Gr17Uu","user_email":"<EMAIL>","user_nicename":"susan_gr17uu","display_name":"Susan Maine","user_registered":"2025-06-18 09:37:30","first_name":"Susan","last_name":"Maine","phone":null,"basic_data":"{\"mobile_number\":\"01245283191\",\"gender\":\"male\",\"dob\":\"1960-03-16\",\"nhs\":null,\"address\":\"27 Third Ave\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4EX\",\"blood_group\":\"\",\"registered_gp_name\":\"LT Waltham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-18 10:37:31"} 
[2025-07-22 10:44:49] local.ERROR: Failed to migrate user ID 584: Session store not set on request. {"ID":"584","user_login":"Susan_1fs2Nm","user_email":"<EMAIL>","user_nicename":"susan_1fs2nm","display_name":"Susan Moore","user_registered":"2025-04-07 07:25:37","first_name":"Susan","last_name":"Moore","phone":null,"basic_data":"{\"mobile_number\":\"07805811603\",\"dob\":\"1954-09-16\",\"nhs\":\"************\",\"address\":\"Hop House, The Old Brewery, Hartford End, Chelmsford, CM3 1JY\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr Stevens\",\"registered_gp_address\":\"JTH Felsted\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 08:25:37"} 
[2025-07-22 10:44:49] local.ERROR: Failed to migrate user ID 283: Session store not set on request. {"ID":"283","user_login":"Susannah_5ARMcs","user_email":"<EMAIL>","user_nicename":"susannah_5armcs","display_name":"Susannah Mann","user_registered":"2025-02-10 14:47:49","first_name":"Susannah","last_name":"Mann","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1967-05-10\",\"nhs\":\"\",\"address\":\"57 Coopers Hill Ongar CM5 9EF\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM5 9EF\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-10 14:47:49"} 
[2025-07-22 10:44:49] local.ERROR: Failed to migrate user ID 433: Session store not set on request. {"ID":"433","user_login":"Suzanne_AdPz7H","user_email":"<EMAIL>","user_nicename":"suzanne_adpz7h","display_name":"Suzanne Warre-dymond","user_registered":"2025-03-11 12:26:38","first_name":"Suzanne","last_name":"Warre-dymond","phone":null,"basic_data":"{\"mobile_number\":\"07850930836\",\"gender\":\"female\",\"dob\":\"1947-03-14\",\"nhs\":null,\"address\":\"107 Woodside\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"SW19 7BA\",\"blood_group\":\"\",\"registered_gp_name\":\"Francis Grove Surgery, Wimbledon\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-11 12:26:38"} 
[2025-07-22 10:44:49] local.ERROR: Failed to migrate user ID 613: Session store not set on request. {"ID":"613","user_login":"Taylor_24uArM","user_email":"<EMAIL>","user_nicename":"taylor_24uarm","display_name":"Taylor Hawkridge","user_registered":"2025-04-11 17:26:15","first_name":"Taylor","last_name":"Hawkridge","phone":null,"basic_data":"{\"mobile_number\":\"07818507124\",\"gender\":\"female\",\"dob\":\"1991-01-11\",\"nhs\":null,\"address\":\"10 Walters Close\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM2 8NU\",\"blood_group\":\"\",\"registered_gp_name\":\"Baddow Village Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-11 18:26:15"} 
[2025-07-22 10:44:50] local.ERROR: Failed to migrate user ID 275: Session store not set on request. {"ID":"275","user_login":"Terence_v8GHN4","user_email":"<EMAIL>","user_nicename":"terence_v8ghn4","display_name":"Terence Sumser","user_registered":"2025-02-09 12:21:00","first_name":"Terence","last_name":"Sumser","phone":null,"basic_data":"{\"mobile_number\":\"01245267687\",\"gender\":\"male\",\"dob\":\"1959-10-29\",\"nhs\":null,\"address\":\"39 Hobarts Close, CM1 2ES\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 12:21:00"} 
[2025-07-22 10:44:50] local.ERROR: Failed to migrate user ID 523: Session store not set on request. {"ID":"523","user_login":"Terry_76U4oj","user_email":"<EMAIL>","user_nicename":"terry_76u4oj","display_name":"Terry Dean","user_registered":"2025-03-25 16:17:26","first_name":"Terry","last_name":"Dean","phone":null,"basic_data":"{\"mobile_number\":\"07927282579\",\"gender\":\"male\",\"dob\":\"1970-09-04\",\"nhs\":null,\"address\":\"73 West Avenue , CM12DD\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM 12DD\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-25 16:17:26"} 
[2025-07-22 10:44:50] local.ERROR: Failed to migrate user ID 1032: Session store not set on request. {"ID":"1032","user_login":"test_dwOTql","user_email":"<EMAIL>","user_nicename":"test_dwotql","display_name":"test fhghgf","user_registered":"2025-07-05 08:47:50","first_name":"test","last_name":"fhghgf","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"2025-06-29\",\"nhs\":\"34534534534\",\"address\":\"Dickens Place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"ggjghjhj\",\"registered_gp_address\":\"Dickens Place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-05 09:47:51"} 
[2025-07-22 10:44:50] local.ERROR: Failed to migrate user ID 1033: Session store not set on request. {"ID":"1033","user_login":"test_0p5VEg","user_email":"<EMAIL>","user_nicename":"test_0p5veg","display_name":"test fhghgf","user_registered":"2025-07-05 08:48:13","first_name":"test","last_name":"fhghgf","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"2025-06-29\",\"nhs\":\"34534534534\",\"address\":\"Dickens Place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"ggjghjhj\",\"registered_gp_address\":\"Dickens Place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-05 09:48:13"} 
[2025-07-22 10:44:50] local.ERROR: Failed to migrate user ID 1013: Session store not set on request. {"ID":"1013","user_login":"Test_IHOJFd","user_email":"<EMAIL>","user_nicename":"test_ihojfd","display_name":"Test Mohan","user_registered":"2025-06-30 12:27:44","first_name":"Test","last_name":"Mohan","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"0001-11-01\",\"nhs\":\"\",\"address\":\"123\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Mohan\",\"registered_gp_address\":\"25 LINGE AVENUE\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 13:27:44"} 
[2025-07-22 10:44:51] local.ERROR: Failed to migrate user ID 411: Session store not set on request. {"ID":"411","user_login":"Test_3wbkc0","user_email":"<EMAIL>","user_nicename":"test_3wbkc0","display_name":"Test Pat","user_registered":"2025-03-06 17:13:10","first_name":"Test","last_name":"Pat","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2025-02-27\",\"nhs\":\"454366\",\"address\":null,\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"test\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-06 17:13:10"} 
[2025-07-22 10:44:51] local.ERROR: Failed to migrate user ID 236: Session store not set on request. {"ID":"236","user_login":"test_2NgkGh","user_email":"<EMAIL>","user_nicename":"test_2ngkgh","display_name":"test patient","user_registered":"2025-01-31 13:55:18","first_name":"test","last_name":"patient","phone":null,"basic_data":"{\"mobile_number\":\"1234567\",\"gender\":\"male\",\"dob\":\"1980-11-10\",\"nhs\":null,\"address\":\"chlemford\",\"city\":\"london\",\"state\":\"\",\"country\":null,\"postal_code\":\"se 18\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-31 13:55:18"} 
[2025-07-22 10:44:51] local.ERROR: Failed to migrate user ID 1026: Session store not set on request. {"ID":"1026","user_login":"test_hDeI9B","user_email":"<EMAIL>","user_nicename":"test_hdei9b","display_name":"test patient","user_registered":"2025-07-04 15:42:37","first_name":"test","last_name":"patient","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-06-29\",\"nhs\":\"67567567567\",\"address\":\"Dickens Place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"asdasdasd\",\"registered_gp_address\":\"Dickens Place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 16:42:37"} 
[2025-07-22 10:44:51] local.ERROR: Failed to migrate user ID 223: Session store not set on request. {"ID":"223","user_login":"Test_0lfuOV","user_email":"<EMAIL>","user_nicename":"test_0lfuov","display_name":"Test Po","user_registered":"2025-01-25 23:54:50","first_name":"Test","last_name":"Po","phone":null,"basic_data":"{\"mobile_number\":\"1234\",\"gender\":\"female\",\"dob\":\"0111-11-11\",\"nhs\":null,\"address\":\"86 Harley Street\",\"city\":\"London\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"W1G 7HP\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-25 23:54:50"} 
[2025-07-22 10:44:52] local.ERROR: Failed to migrate user ID 207: Session store not set on request. {"ID":"207","user_login":"Test_wtRT3s","user_email":"<EMAIL>","user_nicename":"test_wtrt3s","display_name":"Test Roshan","user_registered":"2025-01-22 11:48:28","first_name":"Test","last_name":"Roshan","phone":null,"basic_data":"{\"mobile_number\":\"1233\",\"gender\":\"male\",\"dob\":\"1111-01-01\",\"nhs\":null,\"address\":\"86 Harley Street\",\"city\":\"London\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"W1G 7HP\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-22 11:48:28"} 
[2025-07-22 10:44:52] local.ERROR: Failed to migrate user ID 1010: Session store not set on request. {"ID":"1010","user_login":"test_HYTe0u","user_email":"<EMAIL>","user_nicename":"test_hyte0u","display_name":"test test","user_registered":"2025-06-30 10:49:41","first_name":"test","last_name":"test","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"2025-06-01\",\"nhs\":\"546546456\",\"address\":\"Dickens Place\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"test\",\"registered_gp_address\":\"Dickens Place\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-30 11:49:41"} 
[2025-07-22 10:44:52] local.ERROR: Failed to migrate user ID 1025: Session store not set on request. {"ID":"1025","user_login":"Test_yQUBnC","user_email":"<EMAIL>","user_nicename":"test_yqubnc","display_name":"Test Test","user_registered":"2025-07-04 15:36:18","first_name":"Test","last_name":"Test","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-07-12\",\"nhs\":\"\",\"address\":\"50, Spital Lane\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"test\",\"registered_gp_address\":\"50, Spital Lane\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 16:36:18"} 
[2025-07-22 10:44:52] local.ERROR: Failed to migrate user ID 588: Session store not set on request. {"ID":"588","user_login":"Thirumalai_ZeT3Q9","user_email":"<EMAIL>","user_nicename":"thirumalai_zet3q9","display_name":"Thirumalai Gokul","user_registered":"2025-04-07 11:30:55","first_name":"Thirumalai","last_name":"Gokul","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"2009-01-29\",\"nhs\":null,\"address\":\"94 Lathom Road Eastham E6 2DX\",\"city\":\"Eastham\",\"state\":\"\",\"country\":null,\"postal_code\":\"E6 2DX\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-07 12:30:55"} 
[2025-07-22 10:44:53] local.ERROR: Failed to migrate user ID 775: Session store not set on request. {"ID":"775","user_login":"Thomas_oMIsDE","user_email":"<EMAIL>","user_nicename":"thomas_omisde","display_name":"Thomas Keegan","user_registered":"2025-05-12 14:27:01","first_name":"Thomas","last_name":"Keegan","phone":null,"basic_data":"{\"mobile_number\":\"07710132006\",\"gender\":\"male\",\"dob\":\"1964-05-05\",\"nhs\":\"**********\",\"address\":\"179 Woodland Avenue\",\"city\":\"Brentwood\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM13 1HN\",\"blood_group\":\"\",\"registered_gp_name\":\"Rockleigh Court Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-12 19:31:32"} 
[2025-07-22 10:44:53] local.ERROR: Failed to migrate user ID 322: Session store not set on request. {"ID":"322","user_login":"Thomas_Jt4Kva","user_email":"<EMAIL>","user_nicename":"thomas_jt4kva","display_name":"Thomas Yallop","user_registered":"2025-02-16 13:36:40","first_name":"Thomas","last_name":"Yallop","phone":null,"basic_data":"{\"mobile_number\":\"07572082694\",\"gender\":\"male\",\"dob\":\"1988-05-16\",\"nhs\":null,\"address\":\"34 Toppesfield avenue wickford SS120PB\",\"city\":\"wickford\",\"state\":\"\",\"country\":null,\"postal_code\":\"SS120PB\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-16 13:52:48"} 
[2025-07-22 10:44:53] local.ERROR: Failed to migrate user ID 856: Session store not set on request. {"ID":"856","user_login":"Timothy_OZodxV","user_email":"<EMAIL>","user_nicename":"timothy_ozodxv","display_name":"Timothy Horsey","user_registered":"2025-05-30 17:13:06","first_name":"Timothy","last_name":"Horsey","phone":null,"basic_data":"{\"mobile_number\":\"07989962185\",\"gender\":\"male\",\"dob\":\"1960-08-03\",\"nhs\":null,\"address\":\"St Michaels Cottage, the sweet\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 1HA\",\"blood_group\":\"\",\"registered_gp_name\":\"Little Waltham\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-30 18:13:06"} 
[2025-07-22 10:44:53] local.ERROR: Failed to migrate user ID 220: Session store not set on request. {"ID":"220","user_login":"Todd_Wd4Hi7","user_email":"<EMAIL>","user_nicename":"todd_wd4hi7","display_name":"Todd Garside","user_registered":"2025-01-25 16:38:16","first_name":"Todd","last_name":"Garside","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1996-11-22\",\"nhs\":null,\"address\":\"72 Downstreetfield\",\"city\":null,\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM77 7WP\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-31 16:06:31"} 
[2025-07-22 10:44:54] local.ERROR: Failed to migrate user ID 813: Session store not set on request. {"ID":"813","user_login":"Tolu_ntb9jS","user_email":"<EMAIL>","user_nicename":"tolu_ntb9js","display_name":"Tolu Adeleke","user_registered":"2025-05-20 12:00:44","first_name":"Tolu","last_name":"Adeleke","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1996-09-10\",\"nhs\":null,\"address\":\"151 Kings road\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2BA\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne Surgery\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-20 13:00:44"} 
[2025-07-22 10:44:54] local.ERROR: Failed to migrate user ID 737: Session store not set on request. {"ID":"737","user_login":"Tom_vaT7f0","user_email":"<EMAIL>","user_nicename":"tom_vat7f0","display_name":"Tom Cassidy","user_registered":"2025-05-04 08:23:29","first_name":"Tom","last_name":"Cassidy","phone":null,"basic_data":"{\"mobile_number\":\"07545097775\",\"gender\":\"male\",\"dob\":\"1981-06-04\",\"nhs\":\"**********\",\"address\":\"89 south Primrose Hill Chelmsford Essex CM12RG\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Rivermead Gate medical centre\",\"registered_gp_address\":\"Unit 3&amp;4 Rivermead gateRectory lane Chelmsford, Essex ,Cm11TR\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-20 16:24:15"} 
[2025-07-22 10:44:54] local.ERROR: Failed to migrate user ID 951: Session store not set on request. {"ID":"951","user_login":"Tracey_nr9l5e","user_email":"<EMAIL>","user_nicename":"tracey_nr9l5e","display_name":"Tracey Clark","user_registered":"2025-06-17 05:31:04","first_name":"Tracey","last_name":"Clark","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1968-10-28\",\"nhs\":null,\"address\":\"89 Cherwell Drive\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2JJ\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"89\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-17 10:36:26"} 
[2025-07-22 10:44:54] local.ERROR: Failed to migrate user ID 785: Session store not set on request. {"ID":"785","user_login":"Tracey_jv4Yiq","user_email":"<EMAIL>","user_nicename":"tracey_jv4yiq","display_name":"Tracey Eastell","user_registered":"2025-05-15 14:35:11","first_name":"Tracey","last_name":"Eastell","phone":null,"basic_data":"{\"mobile_number\":\"07876612314\",\"gender\":\"female\",\"dob\":\"1970-11-02\",\"nhs\":\"**********\",\"address\":\"12 Hall Lane Sandon Chelmsford\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"Baddow Hall surgery Longmead Ave\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 15:01:18"} 
[2025-07-22 10:44:55] local.ERROR: Failed to migrate user ID 242: Session store not set on request. {"ID":"242","user_login":"tracy_ipejm7","user_email":"<EMAIL>","user_nicename":"tracy_ipejm7","display_name":"tracy wild","user_registered":"2025-02-02 08:49:22","first_name":"tracy","last_name":"wild","phone":null,"basic_data":"{\"mobile_number\":\"07730680297\",\"gender\":\"female\",\"dob\":\"1965-06-19\",\"nhs\":\"Long Field Medical centre\",\"address\":\"140 Goldhanger rd, Heybridge maldon cm9 4QS\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM9 4QS\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-28 10:34:33"} 
[2025-07-22 10:44:55] local.ERROR: Failed to migrate user ID 272: Session store not set on request. {"ID":"272","user_login":"Vanessa_uzkwvs","user_email":"<EMAIL>","user_nicename":"vanessa_uzkwvs","display_name":"Vanessa Challis","user_registered":"2025-02-09 10:09:11","first_name":"Vanessa","last_name":"Challis","phone":null,"basic_data":"{\"mobile_number\":\"07852910656\",\"gender\":\"female\",\"dob\":\"1969-12-02\",\"nhs\":null,\"address\":\"64 Craiston Way. Great Baddow\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"UK\",\"postal_code\":\"CM2 8EB\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-09 10:09:11"} 
[2025-07-22 10:44:55] local.ERROR: Failed to migrate user ID 1027: Session store not set on request. {"ID":"1027","user_login":"Victoria_9lTANZ","user_email":"<EMAIL>","user_nicename":"victoria_9ltanz","display_name":"Victoria Candler","user_registered":"2025-07-04 16:58:10","first_name":"Victoria","last_name":"Candler","phone":null,"basic_data":"{\"mobile_number\":\"07774259247\",\"gender\":\"female\",\"dob\":\"1989-01-25\",\"nhs\":null,\"address\":\"Dickens Place\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":\"United Kingdom\",\"postal_code\":\"CM1 4UU\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"Dickens Place\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-07-04 17:58:10"} 
[2025-07-22 10:44:55] local.ERROR: Failed to migrate user ID 823: Session store not set on request. {"ID":"823","user_login":"Victoria_PFmokS","user_email":"<EMAIL>","user_nicename":"victoria_pfmoks","display_name":"Victoria Thomas","user_registered":"2025-05-22 11:41:37","first_name":"Victoria","last_name":"Thomas","phone":null,"basic_data":"{\"mobile_number\":\"07903583430\",\"gender\":\"female\",\"dob\":\"1990-10-28\",\"nhs\":null,\"address\":\"queens burrow road\",\"city\":\"southminster\",\"state\":\"\",\"country\":null,\"postal_code\":\"cm1 *\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-23 11:02:21"} 
[2025-07-22 10:44:56] local.ERROR: Failed to migrate user ID 802: Session store not set on request. {"ID":"802","user_login":"Vikram_lyEknL","user_email":"<EMAIL>","user_nicename":"vikram_lyeknl","display_name":"Vikram Vijayaraghavan","user_registered":"2025-05-18 09:48:13","first_name":"Vikram","last_name":"Vijayaraghavan","phone":null,"basic_data":"{\"mobile_number\":\"07561260662\",\"gender\":\"male\",\"dob\":\"1979-04-07\",\"nhs\":null,\"address\":\"FLAT 1, St Marks House, Cottage Place\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 1NL\",\"blood_group\":\"\",\"registered_gp_name\":\"Abroad\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-18 10:48:13"} 
[2025-07-22 10:44:56] local.ERROR: Failed to migrate user ID 1003: Session store not set on request. {"ID":"1003","user_login":"Warwick_d0Jtb6","user_email":"<EMAIL>","user_nicename":"warwick_d0jtb6","display_name":"Warwick Gooch","user_registered":"2025-06-28 11:19:24","first_name":"Warwick","last_name":"Gooch","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"1949-09-16\",\"nhs\":\"\",\"address\":\"2 Roots Lane, Wickham Bishops Wickham Bishops\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"male\",\"registered_gp_name\":\"Fern House Surgery\",\"registered_gp_address\":\"125 Newland st. Whitham\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-28 12:19:24"} 
[2025-07-22 10:44:56] local.ERROR: Failed to migrate user ID 333: Session store not set on request. {"ID":"333","user_login":"Wayne_auxXVH","user_email":"<EMAIL>","user_nicename":"wayne_auxxvh","display_name":"Wayne Jones","user_registered":"2025-02-21 19:15:08","first_name":"Wayne","last_name":"Jones","phone":null,"basic_data":"{\"mobile_number\":\"07861179256\",\"gender\":\"male\",\"dob\":\"1987-05-12\",\"nhs\":null,\"address\":\"71 Ruts Crescent, Chelmsford CM1 3GL\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-21 19:15:08"} 
[2025-07-22 10:44:56] local.ERROR: Failed to migrate user ID 459: Session store not set on request. {"ID":"459","user_login":"Wayne_7yuAeK","user_email":"<EMAIL>","user_nicename":"wayne_7yuaek","display_name":"Wayne Wheeler","user_registered":"2025-03-16 07:58:03","first_name":"Wayne","last_name":"Wheeler","phone":null,"basic_data":"{\"mobile_number\":\"07532406769\",\"gender\":\"male\",\"dob\":\"1966-04-26\",\"nhs\":\"**********\",\"address\":\"5 FLINTWICH Manor Chelmsford CM1 4YP\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":null,\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House Surgery\",\"registered_gp_address\":\"Parkside Medical Centre, Chelmsford, CM1 2DY\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-22 13:35:56"} 
[2025-07-22 10:44:57] local.ERROR: Failed to migrate user ID 237: Session store not set on request. {"ID":"237","user_login":"Wendy_rLgH60","user_email":"<EMAIL>","user_nicename":"wendy_rlgh60","display_name":"Wendy Carmichael","user_registered":"2025-01-31 15:56:42","first_name":"Wendy","last_name":"Carmichael","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"gender\":\"female\",\"dob\":\"1963-05-13\",\"nhs\":null,\"address\":\"42 Priory Road Bicknacre\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 4EY\",\"blood_group\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-01-31 15:56:42"} 
[2025-07-22 10:44:57] local.ERROR: Failed to migrate user ID 263: Session store not set on request. {"ID":"263","user_login":"Wendy_x8jqVA","user_email":"<EMAIL>","user_nicename":"wendy_x8jqva","display_name":"Wendy Carmichael","user_registered":"2025-02-07 08:53:10","first_name":"Wendy","last_name":"Carmichael","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"dob\":\"2025-05-13\",\"nhs\":\"4768 527957\",\"address\":\"42 Priory Road Bicknacre\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"CM3 4EY\",\"gender\":\"female\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-02-07 08:53:10"} 
[2025-07-22 10:44:57] local.ERROR: Failed to migrate user ID 583: Session store not set on request. {"ID":"583","user_login":"Wendy_MCYhzG","user_email":"<EMAIL>","user_nicename":"wendy_mcyhzg","display_name":"Wendy Neal","user_registered":"2025-04-06 19:46:47","first_name":"Wendy","last_name":"Neal","phone":null,"basic_data":"{\"mobile_number\":\"07531971269\",\"dob\":\"1969-06-13\",\"nhs\":\"\",\"address\":\"35 Burnside Crescent Cm1 4eh\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Dr\",\"registered_gp_address\":\"Tenneyson House Surgery, Chelmsford\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-06 20:46:47"} 
[2025-07-22 10:44:57] local.ERROR: Failed to migrate user ID 948: Session store not set on request. {"ID":"948","user_login":"William_8lvEIJ","user_email":"<EMAIL>","user_nicename":"william_8lveij","display_name":"William Kyle","user_registered":"2025-06-16 14:08:05","first_name":"William","last_name":"Kyle","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"male\",\"dob\":\"1948-01-08\",\"nhs\":null,\"address\":\"Floreat Main Road Boreham\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM3 3JB\",\"blood_group\":\"\",\"registered_gp_name\":\"North Chelmsford NHS\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-16 15:37:52"} 
[2025-07-22 10:44:58] local.ERROR: Failed to migrate user ID 929: Session store not set on request. {"ID":"929","user_login":"Yasmin_QFzwDC","user_email":"<EMAIL>","user_nicename":"yasmin_qfzwdc","display_name":"Yasmin Bradford","user_registered":"2025-06-13 17:25:15","first_name":"Yasmin","last_name":"Bradford","phone":null,"basic_data":"{\"mobile_number\":\"07305827343\",\"gender\":\"female\",\"dob\":\"1999-06-03\",\"nhs\":null,\"address\":\"36 Belvawney Close\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 4YR\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-13 18:25:15"} 
[2025-07-22 10:44:58] local.ERROR: Failed to migrate user ID 636: Session store not set on request. {"ID":"636","user_login":"Yuva_SiWbwj","user_email":"<EMAIL>","user_nicename":"yuva_siwbwj","display_name":"Yuva Band","user_registered":"2025-04-14 17:37:30","first_name":"Yuva","last_name":"Band","phone":null,"basic_data":"{\"mobile_number\":\"0**********\",\"dob\":\"2025-04-19\",\"nhs\":\"\",\"address\":\"Cm13gu\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"Yuans a\",\"registered_gp_address\":\"Helen dd\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-04-14 18:37:30"} 
[2025-07-22 10:44:58] local.ERROR: Failed to migrate user ID 204: Session store not set on request. {"ID":"204","user_login":"yuvasri_TlGgwL","user_email":"<EMAIL>","user_nicename":"yuvasri_tlggwl","display_name":"yuvasri venkatesan","user_registered":"2025-01-21 11:12:58","first_name":"yuvasri","last_name":"venkatesan","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"2001-04-12\",\"nhs\":null,\"address\":\"79 parkinson drive\",\"city\":\"chelmsford\",\"state\":\"\",\"country\":\"united kingdom\",\"postal_code\":\"cm13gu\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-19 14:30:32"} 
[2025-07-22 10:44:58] local.ERROR: Failed to migrate user ID 256: Session store not set on request. {"ID":"256","user_login":"Zahra_4JXw0S","user_email":"<EMAIL>","user_nicename":"zahra_4jxw0s","display_name":"Zahra Vine","user_registered":"2025-02-04 11:01:48","first_name":"Zahra","last_name":"Vine","phone":null,"basic_data":"{\"mobile_number\":\"07394532673\",\"gender\":\"female\",\"dob\":\"2008-05-05\",\"nhs\":\"Whitley surgery\",\"address\":\"29 Thames Ave\",\"city\":\"Chelmsford\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM1 2LN\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-21 15:04:05"} 
[2025-07-22 10:44:59] local.ERROR: Failed to migrate user ID 869: Session store not set on request. {"ID":"869","user_login":"Zainab_UkgR75","user_email":"<EMAIL>","user_nicename":"zainab_ukgr75","display_name":"Zainab Rehman","user_registered":"2025-06-01 10:14:25","first_name":"Zainab","last_name":"Rehman","phone":null,"basic_data":"{\"mobile_number\":\"**********\",\"gender\":\"female\",\"dob\":\"1991-04-21\",\"nhs\":null,\"address\":\"12a gerard road\",\"city\":\"harrow\",\"state\":\"\",\"country\":null,\"postal_code\":\"ha1 2nd\",\"blood_group\":\"\",\"registered_gp_name\":\"\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-01 11:14:25"} 
[2025-07-22 10:44:59] local.ERROR: Failed to migrate user ID 894: Session store not set on request. {"ID":"894","user_login":"Zawadi_vdURxW","user_email":"<EMAIL>","user_nicename":"zawadi_vdurxw","display_name":"Zawadi Innes","user_registered":"2025-06-07 10:40:07","first_name":"Zawadi","last_name":"Innes","phone":null,"basic_data":"{\"mobile_number\":\"07875773405\",\"gender\":\"female\",\"dob\":\"1981-02-19\",\"nhs\":null,\"address\":\"25 Park Drive\",\"city\":\"Ingatestone\",\"state\":\"\",\"country\":null,\"postal_code\":\"CM4 9DT\",\"blood_group\":\"\",\"registered_gp_name\":\"New Folly GP Practice\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-06-07 11:40:07"} 
[2025-07-22 10:44:59] local.ERROR: Failed to migrate user ID 466: Session store not set on request. {"ID":"466","user_login":"Zoe_omrZgc","user_email":"<EMAIL>","user_nicename":"zoe_omrzgc","display_name":"Zoe Arnold","user_registered":"2025-03-16 18:22:58","first_name":"Zoe","last_name":"Arnold","phone":null,"basic_data":"{\"mobile_number\":\"07943723013\",\"dob\":\"1991-10-11\",\"nhs\":\"\",\"address\":\"337 Main Road Broomfield Chelmsford CM1 7BB\",\"city\":\"\",\"state\":\"\",\"country\":\"\",\"postal_code\":\"\",\"gender\":\"female\",\"registered_gp_name\":\"North Chelmsford GP\",\"registered_gp_address\":\"2 White Hart Line, Springfield, Chelmsford, CM2 5EF\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-03-16 18:22:58"} 
[2025-07-22 10:44:59] local.ERROR: Failed to migrate user ID 732: Session store not set on request. {"ID":"732","user_login":"Zoe_Ey5c4K","user_email":"<EMAIL>","user_nicename":"zoe_ey5c4k","display_name":"Zoe Morgan","user_registered":"2025-05-01 13:15:38","first_name":"Zoe","last_name":"Morgan","phone":null,"basic_data":"{\"mobile_number\":\"07938100076\",\"gender\":\"female\",\"dob\":\"1974-02-26\",\"nhs\":null,\"address\":\"10 Heather Court\",\"city\":null,\"state\":\"\",\"country\":null,\"postal_code\":\"CM16Y2\",\"blood_group\":\"\",\"registered_gp_name\":\"Melbourne House\",\"registered_gp_address\":\"\",\"insurance_provider\":\"\",\"insurance_no\":\"\"}","user_type":"kiviCare_patient","clinic_id":"14","mapping_created_at":"2025-05-01 14:15:39"} 

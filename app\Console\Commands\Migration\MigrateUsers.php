<?php

namespace App\Console\Commands\Migration;

use App\Models\User;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Users Command
 * 
 * Simple focused migration for users only
 */
class MigrateUsers extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:users
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate users from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for users migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/users");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.users_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/users_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.users_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/users_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'users_migration';
        $this->errorLogChannel = 'users_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING USERS ===");
        $this->logInfo("Starting users migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        $clinicOption = $this->option('clinic') ?? 'all';
        $clinicIds = $this->parseClinicOption($clinicOption);

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalErrors = 0;
        $failedClinics = [];

        foreach ($clinicIds as $clinicId) {
            $this->info("\n--- Migrating users for clinic ID: {$clinicId} ---");
            $this->logInfo("Starting users migration for clinic {$clinicId}");
            
            try {
                // 1. Make API call - get users for this clinic
                $response = $this->makeApiRequest('laravel_get_clinic_users', ['clinic_id' => $clinicId]);
                $wpUsers = $response['data'] ?? [];

                if (empty($wpUsers)) {
                    $this->info("No users found for clinic {$clinicId}");
                    $this->logInfo("No users found for clinic {$clinicId}");
                    continue;
                }

                $this->info("Found " . count($wpUsers) . " users for clinic {$clinicId}");
                $this->logInfo("Found " . count($wpUsers) . " users for clinic {$clinicId}");

                $processed = 0;
                $skipped = 0;
                $errors = 0;

                // 2. Process each user
                foreach ($wpUsers as $wpUser) {
                    try {
                        // Skip admin users - they're system-level, not clinic-specific
                        $wpRole = $this->extractUserRole($wpUser);
                        if (in_array($wpRole, ['administrator', 'admin', 'super_admin'])) {
                            $this->info("Skipped admin user: {$wpUser['display_name']} ({$wpUser['user_email']}) - Role: {$wpRole}");
                            $this->logInfo("Skipped admin user ID {$wpUser['ID']}: {$wpUser['user_email']} - Role: {$wpRole}");
                            $skipped++;
                            continue;
                        }

                        $email = $wpUser['user_email'] ?? null;
                        
                        if ($this->isDryRun()) {
                            // Get the actual role that would be assigned
                            $wpRole = $this->extractUserRole($wpUser);
                            $laravelRole = $this->transformer->convertRole($wpRole);
                            $this->info("Would migrate user: {$email} (WP Role: {$wpRole} → Laravel Role: {$laravelRole})");
                            $this->logInfo("DRY RUN: Would migrate user {$email} with role {$wpRole} → {$laravelRole}");
                            $processed++;
                            continue;
                        }

                        // Skip if email already exists (your email-based approach)
                        if ($email && $this->emailExists($email)) {
                            $this->info("Skipped user: {$email} (Email already exists)");
                            $this->logInfo("Skipped user {$email} - Email already exists");
                            $skipped++;
                            continue;
                        }

                        // Find the Laravel clinic by WordPress clinic ID (since clinics are already migrated)
                        $laravelClinic = \App\Models\Clinic::where('wp_clinic_id', $clinicId)->first();

                        if (!$laravelClinic) {
                            $this->error("Skipped user ID {$wpUser['ID']}: Laravel clinic not found for WordPress clinic {$clinicId}");
                            $this->logError("Skipped user ID {$wpUser['ID']}: Laravel clinic not found", ['wp_clinic_id' => $clinicId]);
                            $skipped++;
                            continue;
                        }

                        // Transform WordPress user data to Laravel format with proper clinic mapping
                        $laravelData = $this->transformer->transformUser($wpUser, $laravelClinic->id);

                        // Create or update user (simple approach)
                        $user = User::updateOrCreate(
                            ['wp_user_id' => $wpUser['ID']],
                            $laravelData
                        );

                        // Create related records based on role
                        $this->createRelatedRecords($user, $wpUser, $laravelClinic->id);

                        $this->info("✓ Migrated user: {$user->email} (Laravel ID: {$user->id}, Role: {$user->role})");
                        $this->logInfo("Successfully migrated user {$user->email} → Laravel ID {$user->id}, Role: {$user->role}");
                        $processed++;

                    } catch (Exception $e) {
                        $this->error("✗ Failed to migrate user ID {$wpUser['ID']}: " . $e->getMessage());
                        $this->logError("Failed to migrate user ID {$wpUser['ID']}: " . $e->getMessage(), $wpUser);
                        $errors++;
                    }
                }

                $this->info("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
                $this->logInfo("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");

                $totalProcessed += $processed;
                $totalSkipped += $skipped;
                $totalErrors += $errors;

            } catch (Exception $e) {
                $this->error("❌ Failed to process clinic {$clinicId}: " . $e->getMessage());
                $this->logError("Failed to process clinic {$clinicId}: " . $e->getMessage());
                $failedClinics[] = $clinicId;
                continue;
            }
        }

        // 3. Generate summary
        $this->generateSummary($totalProcessed, $totalSkipped, $totalErrors, $failedClinics);

        return 0;
    }

    /**
     * Check if email already exists in system
     */
    protected function emailExists($email)
    {
        return User::where('email', $email)->exists();
    }

    /**
     * Extract user role from WordPress user data
     */
    protected function extractUserRole($wpUser)
    {
        // Try different possible role formats from WordPress
        if (isset($wpUser['roles']) && is_array($wpUser['roles']) && !empty($wpUser['roles'])) {
            return $wpUser['roles'][0];
        }
        
        if (isset($wpUser['role']) && !empty($wpUser['role'])) {
            return $wpUser['role'];
        }
        
        if (isset($wpUser['user_role']) && !empty($wpUser['user_role'])) {
            return $wpUser['user_role'];
        }
        
        if (isset($wpUser['meta']['wp_capabilities']) && is_array($wpUser['meta']['wp_capabilities'])) {
            $capabilities = array_keys($wpUser['meta']['wp_capabilities']);
            return $capabilities[0] ?? 'patient';
        }
        
        // Default fallback
        return 'patient';
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 USERS MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }
        
        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some users failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All users migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/users/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }

    /**
     * Create related records (Patient/Provider) based on user role
     */
    protected function createRelatedRecords($user, $wpUser, $clinicId)
    {
        try {
            if ($user->role === 'patient') {
                // Create patient record if it doesn't exist
                if (!$user->patient) {
                    $patientData = $this->transformer->transformPatientData($wpUser, $user->id, $clinicId);
                    \App\Models\Patient::create($patientData);
                    $this->logInfo("Created patient record for user {$user->email}");
                }
            } elseif (in_array($user->role, ['provider', 'clinic_admin'])) {
                // Create provider record if it doesn't exist
                $existingProvider = \App\Models\Provider::where('user_id', $user->id)->first();
                if (!$existingProvider) {
                    $providerData = $this->transformer->transformProviderData($wpUser, $user->id);
                    \App\Models\Provider::create($providerData);
                    $this->logInfo("Created provider record for user {$user->email}");
                }
            }
        } catch (Exception $e) {
            $this->logError("Failed to create related records for user {$user->email}: " . $e->getMessage());
        }
    }
}
